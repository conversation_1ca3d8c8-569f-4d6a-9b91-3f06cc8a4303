### 任务清单

#### 已完成
* [2025-07-03 10:15:48] - UI 布局调整：根据 `computation_mode_spec.md` 更新了 `Entrance.Designer.cs`，重命名了控件并调整了布局。
* [2025-07-03 10:15:48] - 核心逻辑重构：根据 `computation_mode_spec.md` 完全重构了 `Entrance.cs` 中的 `btnCalculate_Click` 事件。
* [2025-07-03 10:15:48] - 文件保存策略实现：在 `Entrance.cs` 中实现了新的文件和目录保存逻辑。
* [2025-07-03 10:15:48] - 梯度图修复：在 `Entrance.cs` 的所有计算路径中添加了梯度图保存逻辑。
* [2025-07-03 10:27:07] - 编译修复：解决了 `prism_bond_analysis.sln` 中的全部 10 个编译错误。
* [2025-07-03 10:31:38] - 文件保存逻辑更新：实现了为大SN、小SN和配对计算创建独立输出文件夹的功能。

#### 进行中
- [ ] 根据测试结果修复潜在的 bug。

#### 待办
- [ ] 最终用户验收。
[2025-07-03 10:17:00] - Started final compilation of prism_bond_analysis.sln.
[2025-07-03 10:18:56] - Failed to compile prism_bond_analysis.sln. 10 errors and 35 warnings.
[2025-07-03 10:27:07] - Successfully compiled prism_bond_analysis.sln. All compilation errors are resolved.
[2025-07-03 10:28:20] - Development team reported compilation errors as fixed. Re-attempting final compilation.
* [2025-07-03 10:39:11] - 功能完成：在双棱镜配对计算期间，为大棱镜和小棱镜的独立结果实现了分离的文件夹保存逻辑。
* [2025-07-03 10:45:54] - 功能完成：为单棱镜（大棱镜和小棱镜）计算增加了分区块计算功能，使其与双棱镜配对计算的逻辑保持一致。

* [2025-07-03 12:22:00] - COMPLETED: Implement asynchronous calculation using `BackgroundWorker` to fix UI freeze.