using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.IO;
using System.Linq;

namespace Nreal_ProductLine_Tool.onlyScan
{
    /// <summary>
    /// Generates a high-resolution, annotated image from calculation results.
    /// </summary>
    public static class AdvancedImageGenerator
    {
        private const int ImageWidth = 2560;  // 增大画布以容纳更多标注
        private const int ImageHeight = 1440;
        private const int Padding = 80;

        public static void Generate(ImageGenerationData data)
        {
            if (data?.AllPoints == null || data.AllPoints.Count == 0 || data.Metrics == null)
            {
                Logs.WriteWarn("AdvancedImageGenerator: Input data is null or empty. Skipping image generation.", true);
                return;
            }

            using (var bitmap = new Bitmap(ImageWidth, ImageHeight))
            using (var graphics = Graphics.FromImage(bitmap))
            {
                graphics.Clear(Color.FromArgb(240, 240, 240)); // Light gray background
                graphics.SmoothingMode = SmoothingMode.AntiAlias;

                var transform = SetupCoordinateTransform(data.AllPoints);

                DrawHeatmap(graphics, data.AllPoints, transform);
                DrawRegionBoundaries(graphics, data.Metrics, transform);
                DrawAnnotations(graphics, data.Metrics, transform);

                // Ensure directory exists
                Directory.CreateDirectory(Path.GetDirectoryName(data.OutputPath));
                bitmap.Save(data.OutputPath, System.Drawing.Imaging.ImageFormat.Png);
            }
        }

        private static Matrix SetupCoordinateTransform(List<DataEntry> points)
        {
            float minX = points.Min(p => p.Col);
            float maxX = points.Max(p => p.Col);
            float minY = points.Min(p => p.Row);
            float maxY = points.Max(p => p.Row);

            float dataWidth = maxX - minX;
            float dataHeight = maxY - minY;

            if (dataWidth == 0 || dataHeight == 0) return new Matrix();

            float scaleX = (float)(ImageWidth - 2 * Padding) / dataWidth;
            float scaleY = (float)(ImageHeight - 2 * Padding) / dataHeight;
            float scale = Math.Min(scaleX, scaleY);

            float offsetX = Padding + ((ImageWidth - 2 * Padding) - dataWidth * scale) / 2f - minX * scale;
            float offsetY = Padding + ((ImageHeight - 2 * Padding) - dataHeight * scale) / 2f - minY * scale;

            var matrix = new Matrix();
            matrix.Translate(offsetX, offsetY);
            matrix.Scale(scale, scale);
            
            return matrix;
        }

        private static void DrawHeatmap(Graphics g, List<DataEntry> points, Matrix transform)
        {
            double minValue = points.Min(p => p.Value);
            double maxValue = points.Max(p => p.Value);

            foreach (var point in points)
            {
                PointF[] transformedPoints = { new PointF(point.Col, point.Row) };
                transform.TransformPoints(transformedPoints);
                PointF p = transformedPoints[0];

                Color color = GetInterpolatedColor(point.Value, minValue, maxValue);
                using (var brush = new SolidBrush(color))
                {
                    g.FillEllipse(brush, p.X - 1, p.Y - 1, 3, 3);
                }
            }
        }

        private static void DrawRegionBoundaries(Graphics g, CalculationMetrics metrics, Matrix transform)
        {
            using (var pen = new Pen(Color.FromArgb(150, Color.Black), 2) { DashStyle = DashStyle.Dash })
            using (var regionPen = new Pen(Color.FromArgb(100, Color.Blue), 1) { DashStyle = DashStyle.Dot })
            using (var font = new Font("Arial", 10, FontStyle.Bold))
            using (var smallFont = new Font("Arial", 8, FontStyle.Regular))
            using (var brush = new SolidBrush(Color.Black))
            using (var regionBrush = new SolidBrush(Color.Blue))
            {
                // 绘制主要区域边界和12个子区域
                DrawRegionWithSubdivisions(g, metrics, transform, pen, regionPen, font, smallFont, brush, regionBrush);
            }
        }

        private static void DrawRegionWithSubdivisions(Graphics g, CalculationMetrics metrics, Matrix transform,
            Pen mainPen, Pen subPen, Font mainFont, Font subFont, Brush mainBrush, Brush subBrush)
        {
            // 获取数据范围来计算子区域边界
            var allPoints = new List<PointF>();

            // 从区域边界计算数据范围
            var regionA = metrics.RegionABounds;
            var regionB = metrics.RegionBBounds;
            var regionC = metrics.RegionCBounds;

            // 计算整体数据范围
            int minCol = Math.Min(Math.Min(regionA.Left, regionB.Left), regionC.Left);
            int maxCol = Math.Max(Math.Max(regionA.Right, regionB.Right), regionC.Right);
            int minRow = Math.Min(Math.Min(regionA.Top, regionB.Top), regionC.Top);
            int maxRow = Math.Max(Math.Max(regionA.Bottom, regionB.Bottom), regionC.Bottom);

            // 计算子区域划分点
            int midRow = (minRow + maxRow) / 2;
            int midCol = (minCol + maxCol) / 2;
            int midRow_1_4 = minRow + (maxRow - minRow) / 4;
            int midRow_3_4 = minRow + (maxRow - minRow) * 3 / 4;
            int Col_1_3 = minCol + (maxCol - minCol) / 3;
            int Col_2_3 = minCol + (maxCol - minCol) * 2 / 3;
            int Col_1_6 = minCol + (maxCol - minCol) / 6;
            int Col_5_6 = minCol + (maxCol - minCol) * 5 / 6;

            // 绘制A区域的4个子区域 (a1-a4)
            DrawSubRegion(g, transform, subPen, subFont, subBrush, "a1",
                Col_1_3, midRow_1_4, midCol, midRow, metrics.a1_pv99, metrics.a1_gradiant_max, metrics.a1_max_row, metrics.a1_max_col);
            DrawSubRegion(g, transform, subPen, subFont, subBrush, "a2",
                midCol, midRow_1_4, Col_2_3, midRow, metrics.a2_pv99, metrics.a2_gradiant_max, metrics.a2_max_row, metrics.a2_max_col);
            DrawSubRegion(g, transform, subPen, subFont, subBrush, "a3",
                midCol, midRow, Col_2_3, midRow_3_4, metrics.a3_pv99, metrics.a3_gradiant_max, metrics.a3_max_row, metrics.a3_max_col);
            DrawSubRegion(g, transform, subPen, subFont, subBrush, "a4",
                Col_1_3, midRow, midCol, midRow_3_4, metrics.a4_pv99, metrics.a4_gradiant_max, metrics.a4_max_row, metrics.a4_max_col);

            // 绘制B区域的4个子区域 (b1-b4)
            DrawSubRegion(g, transform, subPen, subFont, subBrush, "b1",
                Col_1_6, minRow, Col_1_3, midRow_1_4, metrics.b1_pv99, metrics.b1_gradiant_max, metrics.b1_max_row, metrics.b1_max_col);
            DrawSubRegion(g, transform, subPen, subFont, subBrush, "b2",
                Col_2_3, minRow, Col_5_6, midRow_1_4, metrics.b2_pv99, metrics.b2_gradiant_max, metrics.b2_max_row, metrics.b2_max_col);
            DrawSubRegion(g, transform, subPen, subFont, subBrush, "b3",
                Col_2_3, midRow_3_4, Col_5_6, maxRow, metrics.b3_pv99, metrics.b3_gradiant_max, metrics.b3_max_row, metrics.b3_max_col);
            DrawSubRegion(g, transform, subPen, subFont, subBrush, "b4",
                Col_1_6, midRow_3_4, Col_1_3, maxRow, metrics.b4_pv99, metrics.b4_gradiant_max, metrics.b4_max_row, metrics.b4_max_col);

            // 绘制C区域的4个子区域 (c1-c4)
            DrawSubRegion(g, transform, subPen, subFont, subBrush, "c1",
                minCol, minRow, Col_1_6, midRow, metrics.c1_pv99, metrics.c1_gradiant_max, metrics.c1_max_row, metrics.c1_max_col);
            DrawSubRegion(g, transform, subPen, subFont, subBrush, "c2",
                Col_5_6, minRow, maxCol, midRow, metrics.c2_pv99, metrics.c2_gradiant_max, metrics.c2_max_row, metrics.c2_max_col);
            DrawSubRegion(g, transform, subPen, subFont, subBrush, "c3",
                Col_5_6, midRow, maxCol, maxRow, metrics.c3_pv99, metrics.c3_gradiant_max, metrics.c3_max_row, metrics.c3_max_col);
            DrawSubRegion(g, transform, subPen, subFont, subBrush, "c4",
                minCol, midRow, Col_1_6, maxRow, metrics.c4_pv99, metrics.c4_gradiant_max, metrics.c4_max_row, metrics.c4_max_col);
        }

        private static void DrawSubRegion(Graphics g, Matrix transform, Pen pen, Font font, Brush brush,
            string regionName, int left, int top, int right, int bottom,
            double pv99, double gradientMax, double maxRow, double maxCol)
        {
            // 转换区域边界坐标
            PointF[] corners = {
                new PointF(left, top),
                new PointF(right, top),
                new PointF(right, bottom),
                new PointF(left, bottom)
            };
            transform.TransformPoints(corners);

            // 绘制区域边界
            g.DrawPolygon(pen, corners);

            // 计算标注位置（区域中心）
            float centerX = (corners[0].X + corners[2].X) / 2;
            float centerY = (corners[0].Y + corners[2].Y) / 2;

            // 绘制区域标注
            string annotation = $"{regionName}\nPV99: {pv99:F3}\nGrad: {gradientMax:F3}\n({maxRow:F0},{maxCol:F0})";

            // 计算文本大小以调整位置
            SizeF textSize = g.MeasureString(annotation, font);
            float textX = centerX - textSize.Width / 2;
            float textY = centerY - textSize.Height / 2;

            // 绘制半透明背景
            using (var bgBrush = new SolidBrush(Color.FromArgb(200, Color.White)))
            {
                g.FillRectangle(bgBrush, textX - 2, textY - 2, textSize.Width + 4, textSize.Height + 4);
            }

            // 绘制文本
            g.DrawString(annotation, font, brush, textX, textY);
        }

        private static void DrawAnnotations(Graphics g, CalculationMetrics metrics, Matrix transform)
        {
            using (var font = new Font("Arial", 10, FontStyle.Regular))
            using (var boldFont = new Font("Arial", 11, FontStyle.Bold))
            using (var textBrush = new SolidBrush(Color.Black))
            {
                // --- Mark Max Point ---
                PointF[] maxPointSrc = { new PointF((float)metrics.max_col, (float)metrics.max_row) };
                transform.TransformPoints(maxPointSrc);
                PointF maxPoint = maxPointSrc[0];
                g.FillEllipse(Brushes.Red, maxPoint.X - 4, maxPoint.Y - 4, 8, 8);
                g.DrawString($"Max: {metrics.max:F3}", boldFont, textBrush, maxPoint.X + 8, maxPoint.Y - 8);

                // --- Mark Min Point ---
                PointF[] minPointSrc = { new PointF((float)metrics.min_col, (float)metrics.min_row) };
                transform.TransformPoints(minPointSrc);
                PointF minPoint = minPointSrc[0];
                g.FillEllipse(Brushes.Blue, minPoint.X - 4, minPoint.Y - 4, 8, 8);
                g.DrawString($"Min: {metrics.min:F3}", boldFont, textBrush, minPoint.X + 8, minPoint.Y - 8);

                // --- Mark Max Gradient ---
                PointF[] gradPointSrc = { new PointF((float)metrics.gradiant_max_col, (float)metrics.gradiant_max_row) };
                transform.TransformPoints(gradPointSrc);
                PointF gradPoint = gradPointSrc[0];
                g.FillRectangle(Brushes.Purple, gradPoint.X - 4, gradPoint.Y - 4, 8, 8);
                g.DrawString($"Max Grad: {metrics.gradiant_max:F3}", boldFont, textBrush, gradPoint.X + 8, gradPoint.Y - 8);

                // --- Draw Gradient Arrow ---
                using (var arrowPen = new Pen(Color.Purple, 2) { CustomEndCap = new AdjustableArrowCap(5, 5) })
                {
                    float angleRad = (float)(metrics.gradiant_angle * Math.PI / 180.0);
                    float arrowLength = 40;
                    PointF endPoint = new PointF(
                        gradPoint.X + arrowLength * (float)Math.Cos(angleRad),
                        gradPoint.Y + arrowLength * (float)Math.Sin(angleRad)
                    );
                    g.DrawLine(arrowPen, gradPoint, endPoint);
                }
            }
        }

        private static Color GetInterpolatedColor(double value, double min, double max)
        {
            if (min >= max) return Color.Blue;
            double ratio = (value - min) / (max - min);
            int r = (int)(255 * ratio);
            int b = (int)(255 * (1 - ratio));
            return Color.FromArgb(r, 0, b);
        }
    }
}