using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.IO;
using System.Linq;

namespace Nreal_ProductLine_Tool.onlyScan
{
    /// <summary>
    /// Generates a high-resolution, annotated image from calculation results.
    /// </summary>
    public static class AdvancedImageGenerator
    {
        private const int ImageWidth = 1920;
        private const int ImageHeight = 1080;
        private const int Padding = 50;

        public static void Generate(ImageGenerationData data)
        {
            if (data?.AllPoints == null || data.AllPoints.Count == 0 || data.Metrics == null)
            {
                Logs.WriteWarn("AdvancedImageGenerator: Input data is null or empty. Skipping image generation.", true);
                return;
            }

            using (var bitmap = new Bitmap(ImageWidth, ImageHeight))
            using (var graphics = Graphics.FromImage(bitmap))
            {
                graphics.Clear(Color.FromArgb(240, 240, 240)); // Light gray background
                graphics.SmoothingMode = SmoothingMode.AntiAlias;

                var transform = SetupCoordinateTransform(data.AllPoints);

                DrawHeatmap(graphics, data.AllPoints, transform);
                DrawRegionBoundaries(graphics, data.Metrics, transform);
                DrawAnnotations(graphics, data.Metrics, transform);

                // Ensure directory exists
                Directory.CreateDirectory(Path.GetDirectoryName(data.OutputPath));
                bitmap.Save(data.OutputPath, System.Drawing.Imaging.ImageFormat.Png);
            }
        }

        private static Matrix SetupCoordinateTransform(List<DataEntry> points)
        {
            float minX = points.Min(p => p.Col);
            float maxX = points.Max(p => p.Col);
            float minY = points.Min(p => p.Row);
            float maxY = points.Max(p => p.Row);

            float dataWidth = maxX - minX;
            float dataHeight = maxY - minY;

            if (dataWidth == 0 || dataHeight == 0) return new Matrix();

            float scaleX = (float)(ImageWidth - 2 * Padding) / dataWidth;
            float scaleY = (float)(ImageHeight - 2 * Padding) / dataHeight;
            float scale = Math.Min(scaleX, scaleY);

            float offsetX = Padding + ((ImageWidth - 2 * Padding) - dataWidth * scale) / 2f - minX * scale;
            float offsetY = Padding + ((ImageHeight - 2 * Padding) - dataHeight * scale) / 2f - minY * scale;

            var matrix = new Matrix();
            matrix.Translate(offsetX, offsetY);
            matrix.Scale(scale, scale);
            
            return matrix;
        }

        private static void DrawHeatmap(Graphics g, List<DataEntry> points, Matrix transform)
        {
            double minValue = points.Min(p => p.Value);
            double maxValue = points.Max(p => p.Value);

            foreach (var point in points)
            {
                PointF[] transformedPoints = { new PointF(point.Col, point.Row) };
                transform.TransformPoints(transformedPoints);
                PointF p = transformedPoints[0];

                Color color = GetInterpolatedColor(point.Value, minValue, maxValue);
                using (var brush = new SolidBrush(color))
                {
                    g.FillEllipse(brush, p.X - 1, p.Y - 1, 3, 3);
                }
            }
        }

        private static void DrawRegionBoundaries(Graphics g, CalculationMetrics metrics, Matrix transform)
        {
            using (var pen = new Pen(Color.FromArgb(200, Color.Black), 2) { DashStyle = DashStyle.Dash })
            using (var font = new Font("Arial", 12, FontStyle.Bold))
            using (var brush = new SolidBrush(Color.Black))
            {
                // Draw Region A
                if (metrics.RegionABoundary != null && metrics.RegionABoundary.Length == 4)
                {
                    PointF[] transformedRegionA = (PointF[])metrics.RegionABoundary.Clone();
                    transform.TransformPoints(transformedRegionA);
                    g.DrawPolygon(pen, transformedRegionA);
                    g.DrawString("A", font, brush, transformedRegionA[0].X - 20, transformedRegionA[0].Y - 20);
                }

                // Draw Region B
                if (metrics.RegionBBoundary != null && metrics.RegionBBoundary.Length == 4)
                {
                    PointF[] transformedRegionB = (PointF[])metrics.RegionBBoundary.Clone();
                    transform.TransformPoints(transformedRegionB);
                    g.DrawPolygon(pen, transformedRegionB);
                    g.DrawString("B", font, brush, transformedRegionB[0].X - 20, transformedRegionB[0].Y);
                }

                // Draw Region C
                if (metrics.RegionCBoundary != null && metrics.RegionCBoundary.Length == 4)
                {
                    PointF[] transformedRegionC = (PointF[])metrics.RegionCBoundary.Clone();
                    transform.TransformPoints(transformedRegionC);
                    g.DrawPolygon(pen, transformedRegionC);
                    g.DrawString("C", font, brush, transformedRegionC[0].X, transformedRegionC[0].Y - 20);
                }
            }
        }

        private static void DrawAnnotations(Graphics g, CalculationMetrics metrics, Matrix transform)
        {
            using (var font = new Font("Arial", 10, FontStyle.Regular))
            using (var boldFont = new Font("Arial", 11, FontStyle.Bold))
            using (var textBrush = new SolidBrush(Color.Black))
            {
                // --- Mark Max Point ---
                PointF[] maxPointSrc = { new PointF((float)metrics.max_col, (float)metrics.max_row) };
                transform.TransformPoints(maxPointSrc);
                PointF maxPoint = maxPointSrc[0];
                g.FillEllipse(Brushes.Red, maxPoint.X - 4, maxPoint.Y - 4, 8, 8);
                g.DrawString($"Max: {metrics.max:F3}", boldFont, textBrush, maxPoint.X + 8, maxPoint.Y - 8);

                // --- Mark Min Point ---
                PointF[] minPointSrc = { new PointF((float)metrics.min_col, (float)metrics.min_row) };
                transform.TransformPoints(minPointSrc);
                PointF minPoint = minPointSrc[0];
                g.FillEllipse(Brushes.Blue, minPoint.X - 4, minPoint.Y - 4, 8, 8);
                g.DrawString($"Min: {metrics.min:F3}", boldFont, textBrush, minPoint.X + 8, minPoint.Y - 8);

                // --- Mark Max Gradient ---
                PointF[] gradPointSrc = { new PointF((float)metrics.gradiant_max_col, (float)metrics.gradiant_max_row) };
                transform.TransformPoints(gradPointSrc);
                PointF gradPoint = gradPointSrc[0];
                g.FillRectangle(Brushes.Purple, gradPoint.X - 4, gradPoint.Y - 4, 8, 8);
                g.DrawString($"Max Grad: {metrics.gradiant_max:F3}", boldFont, textBrush, gradPoint.X + 8, gradPoint.Y - 8);

                // --- Draw Gradient Arrow ---
                using (var arrowPen = new Pen(Color.Purple, 2) { CustomEndCap = new AdjustableArrowCap(5, 5) })
                {
                    float angleRad = (float)(metrics.gradiant_angle * Math.PI / 180.0);
                    float arrowLength = 40;
                    PointF endPoint = new PointF(
                        gradPoint.X + arrowLength * (float)Math.Cos(angleRad),
                        gradPoint.Y + arrowLength * (float)Math.Sin(angleRad)
                    );
                    g.DrawLine(arrowPen, gradPoint, endPoint);
                }
            }
        }

        private static Color GetInterpolatedColor(double value, double min, double max)
        {
            if (min >= max) return Color.Blue;
            double ratio = (value - min) / (max - min);
            int r = (int)(255 * ratio);
            int b = (int)(255 * (1 - ratio));
            return Color.FromArgb(r, 0, b);
        }
    }
}