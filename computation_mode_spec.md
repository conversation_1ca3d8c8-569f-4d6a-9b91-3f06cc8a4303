# 棱镜计算模式功能规范

**版本:** 1.1
**日期:** 2025-07-03

## 1. 概述

本文档为棱镜计算应用程序的功能扩展提供了详细规范。核心需求是引入三种计算模式：**单大棱镜计算**、**单小棱镜计算**和**双棱镜配对拟合计算**，并根据用户输入自动选择合适的模式。

## 2. 功能需求

### 2.1. UI 交互逻辑

1.  **输入框**:
    *   应用程序界面需包含两个文本输入框：
        *   `txtBigPrismSN` (大棱镜SN)
        *   `txtSmallPrismSN` (小棱镜SN)
    *   系统必须能够解析这两个输入框中的多个序列号（SN）。
    *   SN可以由换行符 (`\n`) 或逗号 (`,`) 分隔。解析时应去除每个SN前后的多余空格。

2.  **触发事件**:
    *   用户点击名为 `btnCalculate` 的“计算”按钮后，启动计算流程。

3.  **UI 布局调整建议**:
    *   为了给SN输入框（特别是 `big_prism_sn_text` 和 `small_prism_sn_text`）提供更充足的显示空间，建议调整控件布局。
    *   在 `Entrance.Designer.cs` 文件中，应修改“拟合面统计”控件 (`groupBox2`) 的 `Location` 属性，将其整体向下移动。

### 2.2. 计算模式判断逻辑

在 `btnCalculate` 的点击事件中，必须执行以下逻辑来确定计算模式：

1.  **获取和解析输入**:
    *   从 `txtBigPrismSN` 获取大棱镜SN列表 (`bigPrismSNs`)。
    *   从 `txtSmallPrismSN` 获取小棱镜SN列表 (`smallPrismSNs`)。

2.  **模式判断**:
    *   **单大棱镜模式 (Single Big Prism Mode)**:
        *   **条件**: `bigPrismSNs` 列表不为空，且 `smallPrismSNs` 列表为空。
        *   **执行**: 对 `bigPrismSNs` 列表中的每一个SN，独立执行计算和保存。
    *   **单小棱镜模式 (Single Small Prism Mode)**:
        *   **条件**: `smallPrismSNs` 列表不为空，且 `bigPrismSNs` 列表为空。
        *   **执行**: 对 `smallPrismSNs` 列表中的每一个SN，独立执行计算和保存。
    *   **双棱镜配对模式 (Dual Prism Pairing Mode)**:
        *   **条件**: `bigPrismSNs` 列表和 `smallPrismSNs` 列表都不为空，并且它们的元素数量完全相等。
        *   **执行**: 对两个列表中的SN按索引配对（例如，`bigPrismSNs[i]` 与 `smallPrismSNs[i]`），对每一对SN执行**三次**独立的计算和保存流程。
    *   **错误状态 (Error State)**:
        *   **条件**:
            *   `bigPrismSNs` 和 `smallPrismSNs` 列表都为空。
            *   `bigPrismSNs` 和 `smallPrismSNs` 列表都不为空，但它们的元素数量不相等。
        *   **执行**:
            *   不执行任何计算。
            *   向用户显示一个明确的错误消息框，说明错误原因（例如，“大棱镜和小棱镜SN数量不匹配，请检查输入。”）。
            *   终止计算流程。

### 2.3. 文件保存策略

根据计算模式，文件保存的目录和文件名结构必须遵循以下规则：

1.  **根目录**: 所有数据都保存在 `data1/` 目录下。

2.  **单大棱镜模式**:
    *   对于每个 `sn`，结果保存在 `data1/{sn}/`。
    *   文件名: `{sn}.png`, `{sn}.csv`, 和 `{sn}_梯度.png`。

3.  **单小棱镜模式**:
    *   对于每个 `sn`，结果保存在 `data1/{sn}/`。
    *   文件名: `{sn}.png`, `{sn}.csv`, 和 `{sn}_梯度.png`。

4.  **双棱镜配对模式**:
    *   对于每对 `bigSn` 和 `smallSn`，会产生三组输出：
        *   **独立大棱镜数据**:
            *   路径: `data1/{bigSn}/`
            *   文件: `{bigSn}.png`, `{bigSn}.csv`, `{bigSn}_梯度.png`
        *   **独立小棱镜数据**:
            *   路径: `data1/{smallSn}/`
            *   文件: `{smallSn}.png`, `{smallSn}.csv`, `{smallSn}_梯度.png`
        *   **配对拟合数据**:
            *   路径: `data1/{bigSn}_{smallSn}/`
            *   文件: `{bigSn}_{smallSn}.png`, `{bigSn}_{smallSn}.csv`, `{bigSn}_{smallSn}_梯度.png`

5.  **梯度热力图**:
    *   **规则**: 在 **所有** 计算（独立和配对）之后，都必须额外生成并保存一张对应的“梯度”热力图。
    *   **命名**: 在原始文件名后追加 `_梯度` 后缀，例如 `{SN}_梯度.png`。

## 3. 核心计算流程 (伪代码)

以下是 `btnCalculate` 点击事件处理程序的重构伪代码。

```pseudocode
FUNCTION onCalculateButtonClick():
    // 1. 获取并解析输入
    bigPrismInput = GET_TEXT_FROM_UI("txtBigPrismSN")
    smallPrismInput = GET_TEXT_FROM_UI("txtSmallPrismSN")

    bigPrismSNs = PARSE_SN_INPUT(bigPrismInput)
    smallPrismSNs = PARSE_SN_INPUT(smallPrismInput)

    // 2. 计算模式判断
    bigCount = COUNT(bigPrismSNs)
    smallCount = COUNT(smallPrismSNs)

    // 错误状态 - 两者都为空
    IF bigCount == 0 AND smallCount == 0 THEN
        SHOW_ERROR_MESSAGE("错误：请输入至少一个大棱镜或小棱镜SN。")
        RETURN
    END IF

    // 模式 1: 单大棱镜
    IF bigCount > 0 AND smallCount == 0 THEN
        FOR EACH sn IN bigPrismSNs:
            result = CALCULATE_SINGLE(sn)
            IF result.isSuccess THEN
                SAVE_RESULTS(sn, result.data)
            ELSE
                LOG_ERROR("计算失败，SN: " + sn)
            END IF
        END FOR
        SHOW_INFO_MESSAGE("单大棱镜计算完成。")

    // 模式 2: 单小棱镜
    ELSE IF smallCount > 0 AND bigCount == 0 THEN
        FOR EACH sn IN smallPrismSNs:
            result = CALCULATE_SINGLE(sn)
            IF result.isSuccess THEN
                SAVE_RESULTS(sn, result.data)
            ELSE
                LOG_ERROR("计算失败，SN: " + sn)
            END IF
        END FOR
        SHOW_INFO_MESSAGE("单小棱镜计算完成。")

    // 模式 3: 双棱镜配对
    ELSE IF bigCount > 0 AND smallCount > 0 AND bigCount == smallCount THEN
        FOR i FROM 0 TO bigCount - 1:
            bigSn = bigPrismSNs[i]
            smallSn = smallPrismSNs[i]

            // --- 三次独立的计算和保存流程 ---

            // 1. 计算并保存独立大棱镜
            bigResult = CALCULATE_SINGLE(bigSn)
            IF bigResult.isSuccess THEN
                SAVE_RESULTS(bigSn, bigResult.data)
            ELSE
                LOG_ERROR("独立大棱镜计算失败，SN: " + bigSn)
            END IF

            // 2. 计算并保存独立小棱镜
            smallResult = CALCULATE_SINGLE(smallSn)
            IF smallResult.isSuccess THEN
                SAVE_RESULTS(smallSn, smallResult.data)
            ELSE
                LOG_ERROR("独立小棱镜计算失败，SN: " + smallSn)
            END IF

            // 3. 计算并保存配对拟合结果
            fitResult = CALCULATE_AND_FIT(bigSn, smallSn)
            IF fitResult.isSuccess THEN
                SAVE_RESULTS_FOR_PAIR(bigSn, smallSn, fitResult.data)
            ELSE
                LOG_ERROR("配对计算失败，大/小SN: " + bigSn + "/" + smallSn)
            END IF
        END FOR
        SHOW_INFO_MESSAGE("双棱镜配对计算完成。")

    // 错误状态 - 数量不匹配
    ELSE
        SHOW_ERROR_MESSAGE("错误：大棱镜和小棱镜SN数量不匹配，请检查输入。")
        RETURN
    END IF
END FUNCTION

// --- 辅助函数定义 ---

FUNCTION PARSE_SN_INPUT(text):
    normalizedText = REPLACE(text, "\n", ",")
    snList = SPLIT(normalizedText, ",")
    cleanedList = []
    FOR EACH sn IN snList:
        trimmedSn = TRIM(sn)
        IF trimmedSn IS NOT EMPTY THEN
            ADD trimmedSn TO cleanedList
        END IF
    END FOR
    RETURN cleanedList
END FUNCTION

FUNCTION SAVE_RESULTS(sn, data):
    // 保存单棱镜的 PNG, CSV 和梯度图
    directoryPath = "data1/" + sn + "/"
    CREATE_DIRECTORY(directoryPath)
    SAVE_IMAGE(directoryPath + sn + ".png", data.image)
    SAVE_CSV(directoryPath + sn + ".csv", data.csv)
    SAVE_IMAGE(directoryPath + sn + "_梯度.png", data.gradientImage) // 保存梯度图
END FUNCTION

FUNCTION SAVE_RESULTS_FOR_PAIR(bigSn, smallSn, data):
    // 保存配对的 PNG, CSV 和梯度图
    combinedSn = bigSn + "_" + smallSn
    directoryPath = "data1/" + combinedSn + "/"
    CREATE_DIRECTORY(directoryPath)
    SAVE_IMAGE(directoryPath + combinedSn + ".png", data.image)
    SAVE_CSV(directoryPath + combinedSn + ".csv", data.csv)
    SAVE_IMAGE(directoryPath + combinedSn + "_梯度.png", data.gradientImage) // 保存梯度图
END FUNCTION

## 4. 待办与技术说明

*   **梯度图保存问题**: 当前梯度图可能未在所有模式下正确保存。需要检查 `CALCULATE_SINGLE` 和 `CALCULATE_AND_FIT` 函数的返回值，确保它们都包含了 `gradientImage` 数据。同时，要验证调用保存逻辑的地方（如 `SAVE_RESULTS`）是否遗漏了对梯度图的保存调用。新的 `SAVE_RESULTS` 伪代码已包含此逻辑，需在代码中实现。