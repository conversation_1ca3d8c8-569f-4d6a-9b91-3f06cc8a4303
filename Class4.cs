﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.IO;

namespace Nreal_ProductLine_Tool.onlyScan
{
    using Nreal_ProductLine_Tool.onlyScan;
    using System;
    using System.Collections.Generic;
    using System.Linq;

    class Corr2
    {
        public static int corr2(string filePath, ref double ud, ref double lr, ref double lr_pv, ref double ud_pv, ref double val, ref int mrow, ref int col, ref double gradient, ref double direction)
        {
            List<DataEntry> entries1 = ReadDataFromFile(filePath);
            Corr(ref entries1, ref ud, ref lr, ref gradient, ref direction);            
            pv(ref entries1, ref lr_pv, ref ud_pv);
            lr_pv = lr_pv * 1000;
            ud_pv = ud_pv * 1000;   
            minVal(ref entries1, ref val, ref mrow, ref col);
            val = val * 1000;

            return 0;
        }

        private static int CalculateGradients(ref List<RowData> matrix1Rows, ref double gradient, ref double direction)
        {
            List<DataEntry1> result = new List<DataEntry1>();
            int step = 0;

            for (int rowIndex = 0; rowIndex < matrix1Rows.Count - 1; rowIndex++)
            {
                var row = matrix1Rows[rowIndex];
                for (int i = 0; i < row.Columns.Count - 1; i++)
                {
                    var currentEntry = row.Columns[i];
                    double leftValue = 0;
                    double rightValue = 0;

                    if (i - step >= 0)
                    {
                        leftValue = row.Columns[i - step].Value * 1000;
                    }
                    if (i + step < row.Columns.Count)
                    {
                        rightValue = row.Columns[i + 1 + step].Value * 1000;
                    }

                    double partialX = (rightValue - leftValue) / (2 * step + (row.Columns[i + 1 + step].Col - row.Columns[i - step].Col));

                    double upValue = leftValue;
                    double downValue = 0;
                    if (rowIndex + step + 1 < matrix1Rows.Count)
                    {
                        var downRow = matrix1Rows[rowIndex + step + 1];
                        var downEntry = downRow.Columns.FirstOrDefault(e => e.Col == currentEntry.Col);
                        if (downEntry == null)
                            continue;

                        downValue = downEntry.Value * 1000;

                    }

                    double partialY = (downValue - upValue) / (2 * step + 1);
                    double gVal = Math.Sqrt(Math.Pow(partialX, 2) + Math.Pow(partialY, 2));
                    result.Add(new DataEntry1
                    {
                        Col = currentEntry.Col,
                        Row = currentEntry.Row,
                        Value = gVal,
                        G_X = partialX,
                        G_Y = partialY
                    });
                }
            }
            int index = result.Count * 5 / 1000;

            var topTenEntries = result.OrderByDescending(entry => entry.Value).Take(index);
            gradient = topTenEntries.Last().Value;
            direction = Math.Atan2(topTenEntries.Last().G_Y, topTenEntries.Last().G_X);
            direction = direction * (180 / Math.PI);
            return 0;
        }

        static int minVal(ref List<DataEntry> dataEntries, ref double val, ref int mrow, ref int col)
        {
            if (dataEntries.Count > 0)
            {
                int firstRow = dataEntries[0].Row;
                int firstCol = dataEntries[0].Col;

                mrow = dataEntries[0].Row;
                col = dataEntries[0].Col;
                val = dataEntries[0].Value;

                foreach (var entry in dataEntries)
                {
                    if (entry.Value < val)
                    {
                        val = entry.Value;
                        mrow = entry.Row;
                        col = entry.Col;
                    }
                    if(entry.Row < firstRow)
                        firstRow = entry.Row;
                    if (entry.Col < firstCol)
                        firstCol = entry.Col;
                }
                mrow -= firstRow;
                col -= firstCol;    
                return 0;
            }
            else
            {
                // Console.WriteLine("文件中没有有效数据。");
                return -1;
            }
        }

        static int Corr(ref List<DataEntry> entries1, ref double ud, ref double lr, ref double gradient, ref double direction)
        {
            var matrix1Rows = entries1.GroupBy(e => e.Row)
            .Select(g => new RowData
            {
                RowNumber = g.Key,
                Columns = g.OrderBy(e => e.Col).ToList()
            })
            .OrderBy(g => g.RowNumber)
            .ToList();

            CalculateGradients(ref matrix1Rows, ref gradient, ref direction);

            // 选取元素最多的行，记录每个 Col
            var maxColumnRow = matrix1Rows.OrderByDescending(r => r.Columns.Count).First();
            var allColumns = maxColumnRow.Columns.Select(c => c.Col).ToList();

            // 补全矩阵
            var completedMatrix = new List<RowData>();
            foreach (var row in matrix1Rows)
            {
                var existingColumns = row.Columns.Select(c => c.Col).ToList();
                var missingColumns = allColumns.Where(c => !existingColumns.Contains(c)).ToList();

                var newRow = new RowData
                {
                    RowNumber = row.RowNumber,
                    Columns = new List<DataEntry>()
                };

                // 添加已有的列
                newRow.Columns.AddRange(row.Columns);

                // 添加缺失的列，值为 0
                foreach (var missingCol in missingColumns)
                {
                    newRow.Columns.Add(new DataEntry
                    {
                        Col = missingCol,
                        Row = row.RowNumber,
                        Value = 0
                    });
                }

                // 对列进行排序
                newRow.Columns = newRow.Columns.OrderBy(c => c.Col).ToList();

                completedMatrix.Add(newRow);
            }

            // 沿中轴左右拆分矩阵
            var leftMatrix = new List<List<double>>();
            var rightMatrix = new List<List<double>>();

            foreach (var row in completedMatrix)
            {
                int mid = row.Columns.Count / 2;
                var leftRow = row.Columns.Take(mid).Select(entry => entry.Value).ToList();
                var rightRow = row.Columns.Skip(mid).Select(entry => entry.Value).ToList();
                rightRow.Reverse();

                leftMatrix.Add(leftRow);
                rightMatrix.Add(rightRow);
            }

            // 计算相关系数
            lr = CalculateCorrelation(leftMatrix, rightMatrix);
            ud = udCorr2(ref completedMatrix);

            return 0;
        }

        static int pv(ref List<DataEntry> entries1, ref double lr_pv, ref double ud_pv)
        {
            var matrix1Rows = entries1.GroupBy(e => e.Row)
               .Select(g => new RowData
               {
                   RowNumber = g.Key,
                   Columns = g.OrderBy(e => e.Col).ToList()
               })
               .OrderBy(g => g.RowNumber)
               .ToList();

            // 统计每行的元素个数
            var counts = matrix1Rows.Select(row => row.Columns.Count).ToList();
            counts.Sort();

            // 1. 找出元素最多的一行
            var rowWithMostElements = matrix1Rows.OrderByDescending(row => row.Columns.Count).First();

            // 2. 找出中点对应的Col值
            int midIndex = rowWithMostElements.Columns.Count / 2;
            int midCol = rowWithMostElements.Columns[midIndex].Col;

            // 3. 分成两个List<DataEntry>
            List<DataEntry> leftList = new List<DataEntry>();
            List<DataEntry> rightList = new List<DataEntry>();

            foreach (var row in matrix1Rows)
            {
                foreach (var entry in row.Columns)
                {
                    if (entry.Col <= midCol)
                    {
                        leftList.Add(entry);
                    }
                    else
                    {
                        rightList.Add(entry);
                    }
                }
            }

            ProcessMatrices(ref leftList, ref rightList, ref lr_pv);

            var maxColData = matrix1Rows.SelectMany(row => row.Columns)
               .GroupBy(entry => entry.Col)
               .OrderByDescending(group => group.Count())
               .First();

            // 计算中点对应的行
            int midIndex1 = maxColData.Count() / 2;
            int midRow = maxColData.ElementAt(midIndex1).Row;

            // 沿水平中轴进行上下分割，下半部分上下翻转
            List<DataEntry> upperList = new List<DataEntry>();
            List<DataEntry> lowerList = new List<DataEntry>();

            // 添加上半部分数据
            for (int i = 0; i < midIndex1; i++)
            {
                upperList.AddRange(matrix1Rows[i].Columns);
            }

            // 处理下半部分数据，先反转行顺序，再调整行号
            var lowerRows = matrix1Rows.Where(r => r.RowNumber >= midRow).Reverse().ToList();
            int newRowNumber = midRow;
            foreach (var row in lowerRows)
            {
                foreach (var entry in row.Columns)
                {
                    DataEntry newEntry = new DataEntry
                    {
                        Col = entry.Col,
                        Row = newRowNumber,
                        Value = entry.Value
                    };
                    lowerList.Add(newEntry);
                }
                newRowNumber++;
            }

            ProcessMatrices1(ref upperList, ref lowerList, ref ud_pv);

            return 0;
        }

        private static int ProcessMatrices1(ref List<DataEntry> entries1, ref List<DataEntry> entries2, ref double pvv)
        {
            var matrix1Rows = entries1.GroupBy(e => e.Row)
                .Select(g => new RowData
                {
                    RowNumber = g.Key,
                    Columns = g.OrderBy(e => e.Col).ToList()
                })
                .OrderBy(g => g.RowNumber)
                .ToList();

            var matrix2Rows = entries2.GroupBy(e => e.Row)
                .Select(g => new RowData
                {
                    RowNumber = g.Key,
                    Columns = g.OrderBy(e => e.Col).ToList()
                })
                .OrderBy(g => g.RowNumber)
                .ToList();

            if (matrix1Rows.Count == 0 || matrix2Rows.Count == 0)
            {
                Console.WriteLine("输入数据不完整");
                return -1;
            }

            // 找出元素个数最多的行
            var maxElementsRow1 = matrix1Rows.OrderByDescending(row => row.Columns.Count).First();
            var maxElementsRow2 = matrix2Rows.OrderByDescending(row => row.Columns.Count).First();

            int midIndex1 = maxElementsRow1.Columns.Count / 2;
            int midIndex2 = maxElementsRow2.Columns.Count / 2;

            int midCol1 = maxElementsRow1.Columns[midIndex1].Col; // 元素个数最多行的对称轴的原始列坐标
            int midCol2 = maxElementsRow2.Columns[midIndex2].Col; // 元素个数最多行的对称轴的原始列坐标

            int c_1 = 0;
            int c_2 = 0;
            List<DataEntry> results = new List<DataEntry>();
            for (int i = 0; i < Math.Min(matrix1Rows.Count, matrix2Rows.Count); i++)
            {
                var m1Row = matrix1Rows[i];
                var m2Row = matrix2Rows[i];

                // 找到当前行中与第一行对称轴列坐标相同的元素的 index
                int currentMidIndex1 = m1Row.Columns.FindIndex(e => e.Col == midCol1);
                int currentMidIndex2 = m2Row.Columns.FindIndex(e => e.Col == midCol2);

                if (currentMidIndex1 == -1 || currentMidIndex2 == -1)
                {
                    Console.WriteLine($"第 {i + 1} 行无法找到对称轴列坐标");
                    continue;
                }
                if (m1Row.Columns.Count > c_1)
                    c_1 = m1Row.Columns.Count;
                if (m2Row.Columns.Count > c_2)
                    c_2 = m2Row.Columns.Count;
                if (i == 0)
                    Console.WriteLine($"c_1:{c_1} c_2:{c_2}");
                int minHalfCols = Math.Min(currentMidIndex1 + 1, currentMidIndex2 + 1);
                minHalfCols = Math.Min(m1Row.Columns.Count / 2 + 1, minHalfCols);
                minHalfCols = Math.Min(m2Row.Columns.Count / 2 + 1, minHalfCols);
                // 处理对称轴及对称轴右侧元素
                for (int offset = 0; offset < minHalfCols; offset++)
                {
                    int index1 = currentMidIndex1 - offset;
                    int index2 = currentMidIndex2 - offset;

                    if (index1 >= 0 && index2 >= 0)
                    {
                        DataEntry m1Entry = m1Row.Columns[index1];
                        DataEntry m2Entry = m2Row.Columns[index2];

                        double sum = m1Entry.Value - m2Entry.Value;
                        results.Add(new DataEntry
                        {
                            Col = m1Entry.Col,
                            Row = m1Entry.Row,
                            Value = sum
                        });
                    }
                }

                // 处理对称轴左侧元素
                for (int offset = 1; offset < minHalfCols; offset++)
                {
                    int index1 = currentMidIndex1 + offset;
                    int index2 = currentMidIndex2 + offset;

                    if (index1 < m1Row.Columns.Count && index2 < m2Row.Columns.Count)
                    {
                        DataEntry m1Entry = m1Row.Columns[index1];
                        DataEntry m2Entry = m2Row.Columns[index2];

                        double sum = m1Entry.Value - m2Entry.Value;
                        results.Add(new DataEntry
                        {
                            Col = m1Entry.Col,
                            Row = m1Entry.Row,
                            Value = sum
                        });
                    }
                }
            }
            Console.WriteLine($"{c_1} {c_2}");

            pvv = CalculatePV99(results);
            //DrawData(results, "output.png");
            //HeatmapGenerator.Generate(results, outFile);
            //SaveDataToCsv(results, "Data/1.csv");

            return 0;
        }

        private static int ProcessMatrices(ref List<DataEntry> entries1, ref List<DataEntry> entries2, ref double pvv)
        {
            var matrix1Rows = entries1.GroupBy(e => e.Row)
                .Select(g => new RowData
                {
                    RowNumber = g.Key,
                    Columns = g.OrderBy(e => e.Col).ToList()
                })
                .OrderBy(g => g.RowNumber)
                .ToList();

            var matrix2Rows = entries2.GroupBy(e => e.Row)
                .Select(g => new RowData
                {
                    RowNumber = g.Key,
                    Columns = g.OrderBy(e => e.Col).ToList()
                })
                .OrderBy(g => g.RowNumber)
                .ToList();

            if (matrix1Rows.Count == 0 || matrix2Rows.Count == 0)
            {
                Console.WriteLine("输入数据不完整");
                return -1;
            }

            // 找出元素个数最多的行
            var maxElementsRow1 = matrix1Rows.OrderByDescending(row => row.Columns.Count).First();
            var maxElementsRow2 = matrix2Rows.OrderByDescending(row => row.Columns.Count).First();

            int midIndex1 = maxElementsRow1.Columns.Count / 2;
            int midIndex2 = maxElementsRow2.Columns.Count / 2;

            int midCol1 = maxElementsRow1.Columns[midIndex1].Col; // 元素个数最多行的对称轴的原始列坐标
            int midCol2 = maxElementsRow2.Columns[midIndex2].Col; // 元素个数最多行的对称轴的原始列坐标

            int c_1 = 0;
            int c_2 = 0;
            List<DataEntry> results = new List<DataEntry>();
            for (int i = 0; i < Math.Min(matrix1Rows.Count, matrix2Rows.Count); i++)
            {
                var m1Row = matrix1Rows[i];
                var m2Row = matrix2Rows[i];

                // 找到当前行中与第一行对称轴列坐标相同的元素的 index
                int currentMidIndex1 = m1Row.Columns.FindIndex(e => e.Col == midCol1);
                int currentMidIndex2 = m2Row.Columns.FindIndex(e => e.Col == midCol2);

                if (currentMidIndex1 == -1 || currentMidIndex2 == -1)
                {
                    Console.WriteLine($"第 {i + 1} 行无法找到对称轴列坐标");
                    continue;
                }
                if (m1Row.Columns.Count > c_1)
                    c_1 = m1Row.Columns.Count;
                if (m2Row.Columns.Count > c_2)
                    c_2 = m2Row.Columns.Count;
                if (i == 0)
                    Console.WriteLine($"c_1:{c_1} c_2:{c_2}");
                int minHalfCols = Math.Min(currentMidIndex1 + 1, currentMidIndex2 + 1);
                minHalfCols = Math.Min(m1Row.Columns.Count / 2 + 1, minHalfCols);
                minHalfCols = Math.Min(m2Row.Columns.Count / 2 + 1, minHalfCols);
                // 处理对称轴及对称轴右侧元素
                for (int offset = 0; offset < minHalfCols; offset++)
                {
                    int index1 = currentMidIndex1 + offset;
                    int index2 = currentMidIndex2 - offset;

                    if (index1 < m1Row.Columns.Count && index2 >= 0)
                    {
                        DataEntry m1Entry = m1Row.Columns[index1];
                        DataEntry m2Entry = m2Row.Columns[index2];

                        double sum = m1Entry.Value - m2Entry.Value;
                        results.Add(new DataEntry
                        {
                            Col = m1Entry.Col,
                            Row = m1Entry.Row,
                            Value = sum
                        });
                    }
                }

                // 处理对称轴左侧元素
                for (int offset = 1; offset < minHalfCols; offset++)
                {
                    int index1 = currentMidIndex1 - offset;
                    int index2 = currentMidIndex2 + offset;

                    if (index1 >= 0 && index2 < m2Row.Columns.Count)
                    {
                        DataEntry m1Entry = m1Row.Columns[index1];
                        DataEntry m2Entry = m2Row.Columns[index2];

                        double sum = m1Entry.Value - m2Entry.Value;
                        results.Add(new DataEntry
                        {
                            Col = m1Entry.Col,
                            Row = m1Entry.Row,
                            Value = sum
                        });
                    }
                }
            }
            //Console.WriteLine($"{c_1} {c_2}");

            pvv = CalculatePV99(results);
            //DrawData(results, "output.png");
            //HeatmapGenerator.Generate(results, outFile);
            //SaveDataToCsv(results, "Data/1.csv");

            return 0;
        }

        private static double CalculatePV99(List<DataEntry> results)
        {
            // 提取所有值
            List<double> values = results.Select(e => e.Value).ToList();

            // 排序
            values.Sort();

            // 计算需要去除的数据量
            int count = (int)Math.Ceiling(values.Count * 0.005);

            // 如果数据量太少，直接返回 0
            if (count * 2 >= values.Count)
            {
                Console.WriteLine("数据量不足，无法计算 PV99");
                return -999;
            }

            //count = 0;
            // 去掉最小和最大的 0.5% 数据
            List<double> filteredValues = values.Skip(count).Take(values.Count - 2 * count).ToList();

            // 计算 PV99
            double minValue = filteredValues.Min();
            double maxValue = filteredValues.Max();
            //GlobalData.pv99_val = maxValue - minValue;
            Console.WriteLine("PV99:" + (maxValue - minValue));
            return maxValue - minValue;
        }

        
        static double udCorr2(ref List<RowData> matrix1Rows)
        {
            // 找出水平中轴，假设矩阵行数为偶数，取中间两行的分界线；奇数则取中间行之后为下部分
            int midRowIndex = matrix1Rows.Count / 2;

            // 沿水平中轴拆分矩阵，下面的矩阵上下翻转
            var upperMatrix = new List<List<double>>();
            var lowerMatrix = new List<List<double>>();

            for (int i = 0; i < matrix1Rows.Count; i++)
            {
                var rowValues = matrix1Rows[i].Columns.Select(entry => entry.Value).ToList();
                if (i < midRowIndex)
                {
                    upperMatrix.Add(rowValues);
                }
                else
                {
                    lowerMatrix.Add(rowValues);
                }
            }

            lowerMatrix.Reverse(); // 对下面的矩阵进行上下翻转

            // 计算相关系数
            double correlation = CalculateCorrelation(upperMatrix, lowerMatrix);

            return correlation;
        }
        static void WriteToCsv(List<RowData> matrix1Rows, string filePath)
        {
            using (StreamWriter writer = new StreamWriter(filePath))
            {
                foreach (var row in matrix1Rows)
                {
                    var values = row.Columns.Select(entry => entry.Value.ToString());
                    string csvLine = string.Join(",", values);
                    writer.WriteLine(csvLine);
                }
            }
        }

        static double CalculateCorrelation(List<List<double>> matrix1, List<List<double>> matrix2)
        {
            int rows = matrix1.Count;
            int cols = matrix1[0].Count;

            // 计算矩阵 A 的均值
            double meanA = matrix1.SelectMany(row => row).Sum() / (rows * cols);
            // 计算矩阵 B 的均值
            double meanB = matrix2.SelectMany(row => row).Sum() / (rows * cols);

            double numerator = 0;
            double denominator1 = 0;
            double denominator2 = 0;

            for (int i = 0; i < rows; i++)
            {
                for (int j = 0; j < cols; j++)
                {
                    double diffA = matrix1[i][j] - meanA;
                    double diffB = matrix2[i][j] - meanB;

                    numerator += diffA * diffB;
                    denominator1 += diffA * diffA;
                    denominator2 += diffB * diffB;
                }
            }

            double denominator = Math.Sqrt(denominator1 * denominator2);

            return numerator / denominator;
        }

        static int CalculateMinPos(ref List<RowData> matrix1Rows, ref int row, ref int col)
        {
            if (matrix1Rows.Count > 0)
            {
                DataEntry minEntry = matrix1Rows[0].Columns[0];
                foreach (var rowData in matrix1Rows)
                {
                    foreach (var entry in rowData.Columns)
                    {
                        if (entry.Value < minEntry.Value)
                        {
                            minEntry = entry;
                        }
                    }
                }
                row = minEntry.Row;
                col = minEntry.Col;
                //Console.WriteLine($"最小值 {minEntry.Value} 对应的坐标为 (Row: {minEntry.Row}, Col: {minEntry.Col})");
                return 0;
            }
            else
            {
                //Console.WriteLine("数据列表为空。");
                return -1;
            }
        }

        private static List<DataEntry> ReadDataFromFile(string filePath)
        {
            List<DataEntry> entries = new List<DataEntry>();
            try
            {
                string[] lines = File.ReadAllLines(filePath);
                foreach (string line in lines)
                {
                    string[] parts = line.Split(new[] { ' ' }, StringSplitOptions.RemoveEmptyEntries);
                    if (parts.Length != 3) continue;
                    entries.Add(new DataEntry
                    {
                        Col = int.Parse(parts[0]),
                        Row = int.Parse(parts[1]),
                        Value = double.Parse(parts[2])
                    });
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"读取文件时出错: {ex.Message}");
            }
            return entries;
        }
    }
}
