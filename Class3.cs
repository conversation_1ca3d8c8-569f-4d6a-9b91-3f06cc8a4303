﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.IO;

namespace prism_bond
{
    using Nreal_ProductLine_Tool.onlyScan;
    using System;
    using System.Collections.Generic;
    using System.Linq;

    class Program
    {
        static int CheckFileExists(string filePath)
        {
            Logs.WriteInfo("check file:" + filePath, true);
            if (File.Exists(filePath))
            {
                return 0;
            }
            else
            {
                return -1;
            }
        }

        public static int ConvertFormat(string inputFilePath, string outputFilePath)
        {
            int ret = CheckFileExists(inputFilePath);
            if(ret != 0)
                return ret;
            // 从文件读取数据并处理
            List<List<DataEntry>> datasets = ProcessInputFromFile(inputFilePath);

            // 找出连续行个数最多的数据集
            List<DataEntry> largestDataset = FindLargestConsecutiveDataset(datasets);

            var matrix1Rows = largestDataset.GroupBy(e => e.Row)
            .Select(g => new RowData
            {
                RowNumber = g.Key,
                Columns = g.OrderBy(e => e.Col).ToList()
            })
            .OrderBy(g => g.RowNumber)
            .ToList();

            // 统计每行的元素个数
            var counts = matrix1Rows.Select(row => row.Columns.Count).ToList();

            // 计算元素个数的均值
            double meanCount = counts.Average();

            // 计算一半均值
            double halfMean = meanCount / 2;

            // 删除元素个数小于一半均值的行
            matrix1Rows = matrix1Rows.Where(row => row.Columns.Count >= halfMean).ToList();

            // 将连续行个数最多的数据集写入文件
            WriteProcessedRowsToFile(outputFilePath, matrix1Rows);
            Logs.WriteInfo($"数据集已写入文件: {outputFilePath}", true);

            return 0;
        }

        private static List<List<DataEntry>> ProcessInputFromFile(string filePath)
        {
            List<List<DataEntry>> datasets = new List<List<DataEntry>>();
            List<DataEntry> currentDataset = new List<DataEntry>();
            int? previousRow = null;

            try
            {
                // 读取文件中的所有行
                string[] lines = File.ReadAllLines(filePath);

                foreach (string line in lines)
                {
                    string[] parts = line.Split(new[] { ' ' }, StringSplitOptions.RemoveEmptyEntries);

                    // 判断是否为有效数据
                    if (parts.Length == 3 && double.TryParse(parts[2], out double value))
                    {
                        int currentRow = int.Parse(parts[1]);

                        if (previousRow.HasValue)
                        {
                            if (currentRow - previousRow.Value > 1)
                            {
                                // 行号增加超过 1，开始新的数据集
                                if (currentDataset.Count > 0)
                                {
                                    datasets.Add(currentDataset);
                                    currentDataset = new List<DataEntry>();
                                }
                            }
                        }

                        // 添加到当前数据集
                        currentDataset.Add(new DataEntry
                        {
                            Col = int.Parse(parts[0]),
                            Row = currentRow,
                            Value = value
                        });

                        previousRow = currentRow;
                    }
                }

                // 添加最后一个数据集（如果有）
                if (currentDataset.Count > 0)
                {
                    datasets.Add(currentDataset);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"读取文件时出错: {ex.Message}");
            }

            return datasets;
        }

        private static List<DataEntry> FindLargestConsecutiveDataset(List<List<DataEntry>> datasets)
        {
            List<DataEntry> largestDataset = new List<DataEntry>();
            int maxRows = 0;

            foreach (var dataset in datasets)
            {
                int rowCount = dataset.GroupBy(entry => entry.Row).Count();
                if (rowCount > maxRows)
                {
                    maxRows = rowCount;
                    largestDataset = dataset;
                }
            }

            return largestDataset;
        }

        private static void WriteProcessedRowsToFile(string filePath, List<RowData> processedRows)
        {
            try
            {
                using (StreamWriter writer = new StreamWriter(filePath))
                {
                    foreach (var row in processedRows)
                    {
                        foreach (var entry in row.Columns)
                        {
                            writer.WriteLine($"{entry.Col} {entry.Row} {entry.Value}");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"写入文件时出错: {ex.Message}");
            }
        }
    }
}
