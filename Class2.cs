using System;
using System.Collections.Generic;
using System.Linq;
using System.IO;
using System.Drawing;

namespace Nreal_ProductLine_Tool.onlyScan
{
    public class DataEntry
    {
        public int Col { get; set; }
        public int Row { get; set; }
        public double Value { get; set; }
    }

    public class DataEntry1
    {
        public int Col { get; set; }
        public int Row { get; set; }
        public double Value { get; set; }
        public double G_X { get; set; }
        public double G_Y { get; set; }
    }

    public class RowData
    {
        public int RowNumber { get; set; }
        public List<DataEntry> Columns { get; set; }
    }


    class Program1
    {
        public static int pv_calculate(string filePath1, string filePath2, string outFile)
        {
            // 从文件中读取数据
            List<DataEntry> entries1 = ReadDataFromFile(filePath1);
            List<DataEntry> entries2 = ReadDataFromFile(filePath2);

            // 合并两个矩阵的数据
            List<DataEntry> allEntries = new List<DataEntry>();
            allEntries.AddRange(entries1);
            allEntries.AddRange(entries2);

            // 分组并处理矩阵
            return ProcessMatrices(entries1, entries2, outFile);
        }

        private static List<DataEntry> ReadDataFromFile(string filePath)
        {
            List<DataEntry> entries = new List<DataEntry>();
            try
            {
                string[] lines = File.ReadAllLines(filePath);
                foreach (string line in lines)
                {
                    string[] parts = line.Split(new[] { ' ' }, StringSplitOptions.RemoveEmptyEntries);
                    if (parts.Length != 3) continue;
                    entries.Add(new DataEntry
                    {
                        Col = int.Parse(parts[0]),
                        Row = int.Parse(parts[1]),
                        Value = double.Parse(parts[2])
                    });
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"读取文件时出错: {ex.Message}");
            }
            return entries;
        }

        private static List<DataEntry> ProcessInput(string input)
        {
            List<DataEntry> entries = new List<DataEntry>();
            string[] lines = input.Split(new[] { '\n', '\r' }, StringSplitOptions.RemoveEmptyEntries);
            foreach (string line in lines)
            {
                string[] parts = line.Split(new[] { ' ' }, StringSplitOptions.RemoveEmptyEntries);
                if (parts.Length != 3) continue;
                entries.Add(new DataEntry
                {
                    Col = int.Parse(parts[0]),
                    Row = int.Parse(parts[1]),
                    Value = double.Parse(parts[2])
                });
            }
            return entries;
        }

        private static int ProcessMatrices(List<DataEntry> entries1, List<DataEntry> entries2, string outFile)
        {
            var matrix1Rows = entries1.GroupBy(e => e.Row)
                .Select(g => new RowData
                {
                    RowNumber = g.Key,
                    Columns = g.OrderBy(e => e.Col).ToList()
                })
                .OrderBy(g => g.RowNumber)
                .ToList();

            var matrix2Rows = entries2.GroupBy(e => e.Row)
                .Select(g => new RowData
                {
                    RowNumber = g.Key,
                    Columns = g.OrderBy(e => e.Col).ToList()
                })
                .OrderBy(g => g.RowNumber)
                .ToList();

            if (matrix1Rows.Count == 0 || matrix2Rows.Count == 0)
            {
                Console.WriteLine("输入数据不完整");
                return -1;
            }

            // 找出元素个数最多的行
            var maxElementsRow1 = matrix1Rows.OrderByDescending(row => row.Columns.Count).First();
            var maxElementsRow2 = matrix2Rows.OrderByDescending(row => row.Columns.Count).First();

            int midIndex1 = maxElementsRow1.Columns.Count / 2;
            int midIndex2 = maxElementsRow2.Columns.Count / 2;

            int midCol1 = maxElementsRow1.Columns[midIndex1].Col; // 元素个数最多行的对称轴的原始列坐标
            int midCol2 = maxElementsRow2.Columns[midIndex2].Col; // 元素个数最多行的对称轴的原始列坐标

            int c_1 = 0;
            int c_2 = 0;
            List<DataEntry> results = new List<DataEntry>();
            for (int i = 0; i < Math.Min(matrix1Rows.Count, matrix2Rows.Count); i++)
            {
                var m1Row = matrix1Rows[i];
                var m2Row = matrix2Rows[i];

                // 找到当前行中与第一行对称轴列坐标相同的元素的 index
                int currentMidIndex1 = m1Row.Columns.FindIndex(e => e.Col == midCol1);
                int currentMidIndex2 = m2Row.Columns.FindIndex(e => e.Col == midCol2);

                if (currentMidIndex1 == -1 || currentMidIndex2 == -1)
                {
                    Console.WriteLine($"第 {i + 1} 行无法找到对称轴列坐标");
                    continue;
                }
                if (m1Row.Columns.Count > c_1)
                    c_1 = m1Row.Columns.Count;
                if (m2Row.Columns.Count > c_2)
                    c_2 = m2Row.Columns.Count;
                if(i==0)
                    Console.WriteLine($"c_1:{c_1} c_2:{c_2}");
                int minHalfCols = Math.Min(currentMidIndex1 + 1, currentMidIndex2 + 1);
                minHalfCols = Math.Min(m1Row.Columns.Count / 2 + 1, minHalfCols);
                minHalfCols = Math.Min(m2Row.Columns.Count / 2 + 1, minHalfCols);
                // 处理对称轴及对称轴右侧元素
                for (int offset = 0; offset < minHalfCols; offset++)
                {
                    int index1 = currentMidIndex1 + offset;
                    int index2 = currentMidIndex2 - offset;

                    if (index1 < m1Row.Columns.Count && index2 >= 0)
                    {
                        DataEntry m1Entry = m1Row.Columns[index1];
                        DataEntry m2Entry = m2Row.Columns[index2];

                        double sum = m1Entry.Value + m2Entry.Value;
                        results.Add(new DataEntry
                        {
                            Col = m1Entry.Col,
                            Row = m1Entry.Row,
                            Value = sum
                        });
                    }
                }

                // 处理对称轴左侧元素
                for (int offset = 1; offset < minHalfCols; offset++)
                {
                    int index1 = currentMidIndex1 - offset;
                    int index2 = currentMidIndex2 + offset;

                    if (index1 >= 0 && index2 < m2Row.Columns.Count)
                    {
                        DataEntry m1Entry = m1Row.Columns[index1];
                        DataEntry m2Entry = m2Row.Columns[index2];

                        double sum = m1Entry.Value + m2Entry.Value;
                        results.Add(new DataEntry
                        {
                            Col = m1Entry.Col,
                            Row = m1Entry.Row,
                            Value = sum
                        });
                    }
                }
            }
            Console.WriteLine($"{c_1} {c_2}");
            foreach (var entry in results.OrderBy(e => e.Row).ThenBy(e => e.Col))
            {
                string valueStr = entry.Value.ToString("0.00");
                valueStr = valueStr.TrimEnd('0').TrimEnd('.');
                if (valueStr == "-0.00" || valueStr == "-0")
                    valueStr = "0";
                //Console.WriteLine($"{entry.Col} {entry.Row} {valueStr}");
            }

            CalculatePV99(results);
            //DrawData(results, "output.png");
            HeatmapGenerator.Generate(results, outFile);
            //SaveDataToCsv(results, "Data/1.csv");

            return 0;
        }

        static void SaveDataToCsv(List<DataEntry> data, string filePath)
        {
            // 获取数据的行列范围
            int minRow = data.Min(e => e.Row);
            int maxRow = data.Max(e => e.Row);
            int minCol = data.Min(e => e.Col);
            int maxCol = data.Max(e => e.Col);

            // 创建二维数组存储数据
            double?[,] grid = new double?[maxRow + 1, maxCol + 1];

            // 填充数据到二维数组
            foreach (var entry in data)
            {
                grid[entry.Row, entry.Col] = entry.Value;
            }

            // 写入CSV文件
            using (var writer = new StreamWriter(filePath))
            {
                for (int r = minRow; r <= maxRow; r++)
                {
                    var rowValues = new List<string>();
                    for (int c = minCol; c <= maxCol; c++)
                    {
                        // 如果值存在则写入，否则写入空值
                        rowValues.Add(grid[r, c]?.ToString("F6") ?? "");
                    }
                    writer.WriteLine(string.Join(",", rowValues));
                }
            }
        }

        private static void CalculatePV99(List<DataEntry> results)
        {
            // 提取所有值
            List<double> values = results.Select(e => e.Value).ToList();

            // 排序
            values.Sort();

            double minValue = values.Min();
            double maxValue = values.Max();
            GlobalData.pv_val = maxValue - minValue;

            // 计算需要去除的数据量
            int count = (int)Math.Ceiling(values.Count * 0.005);

            // 如果数据量太少，直接返回 0
            if (count * 2 >= values.Count)
            {
                Console.WriteLine("数据量不足，无法计算 PV99");
                return;
            }

            //count = 0;
            // 去掉最小和最大的 0.5% 数据
            List<double> filteredValues = values.Skip(count).Take(values.Count - 2 * count).ToList();

            // 计算 PV99
            minValue = filteredValues.Min();
            maxValue = filteredValues.Max();
            GlobalData.pv99_val = maxValue - minValue;
            //Console.WriteLine("PV99:"+ pv99);
            return ;
        }

        static void DrawData(List<DataEntry> data, string outputPath)
        {
            // 参数配置（针对500x300优化）
            int cellSize = 2;        // 每个数据点占2x2像素
            int padding = 10;        // 边距
            int pointRadius = 1;     // 点半径（1像素）
            int labelThreshold = 50; // 行列数超过此值时隐藏标签

            // 计算数据范围
            double minValue = data.Min(e => e.Value * 1000);
            double maxValue = data.Max(e => e.Value * 1000);

            // 转换行列号为0-based索引
            int maxRowIndex = data.Max(e => e.Row) - 1;
            int maxColIndex = data.Max(e => e.Col) - 1;

            // 计算画布尺寸
            int canvasWidth = maxColIndex * cellSize + cellSize + padding * 2;
            int canvasHeight = maxRowIndex * cellSize + cellSize + padding * 2;

            // 自动缩放逻辑（当画布超过4096像素时自动缩小）
            while (canvasWidth > 4096 || canvasHeight > 4096)
            {
                cellSize = Math.Max(1, cellSize - 1);
                canvasWidth = maxColIndex * cellSize + cellSize + padding * 2;
                canvasHeight = maxRowIndex * cellSize + cellSize + padding * 2;
            }

            using (Bitmap bmp = new Bitmap(canvasWidth, canvasHeight))
            using (Graphics g = Graphics.FromImage(bmp))
            {
                g.Clear(Color.White);

                // 内容区域居中计算
                int contentWidth = (maxColIndex + 1) * cellSize;
                int contentHeight = (maxRowIndex + 1) * cellSize;
                int startX = (canvasWidth - contentWidth) / 2;
                int startY = (canvasHeight - contentHeight) / 2;

                foreach (var entry in data)
                {
                    // 转换行列号为坐标
                    int x = startX + (entry.Col - 1) * cellSize;
                    int y = startY + (entry.Row - 1) * cellSize;

                    // 计算颜色
                    double scaledValue = entry.Value * 1000;
                    Color color = GetInterpolatedColor(scaledValue, minValue, maxValue);

                    // 绘制像素点（优化绘制速度）
                    for (int i = 0; i < cellSize; i++)
                    {
                        for (int j = 0; j < cellSize; j++)
                        {
                            bmp.SetPixel(x + i, y + j, color);
                        }
                    }

                    // 仅在低密度时显示标签
                    if (maxColIndex < labelThreshold && maxRowIndex < labelThreshold)
                    {
                        string label = ((int)scaledValue).ToString();
                        g.DrawString(label, new Font("Arial", 6), Brushes.Black, x, y);
                    }
                }

                bmp.Save(outputPath);
            }
        }

        static Color GetInterpolatedColor(double value, double min, double max)
        {
            // 处理单一颜色情况
            if (min == max) return Color.Blue;

            // 线性归一化（包含负数处理）
            double ratio = (value - min) / (max - min);

            // 从蓝色(0,0,255)渐变到红色(255,0,0)
            return Color.FromArgb(
                (int)(255 * ratio),
                0,
                (int)(255 * (1 - ratio))
            );
        }

        static void DrawColorLegend(Graphics g, double min, double max, int x, int y)
        {
            int legendWidth = 100;
            int legendHeight = 20;

            // 绘制渐变条
            for (int i = 0; i < legendWidth; i++)
            {
                double ratio = (double)i / legendWidth;
                Color color = Color.FromArgb(
                    (int)(255 * ratio),
                    0,
                    (int)(255 * (1 - ratio))
                );
                using (Pen p = new Pen(color))
                {
                    g.DrawLine(p, x + i, y, x + i, y + legendHeight);
                }
            }

            // 添加刻度标签
            g.DrawString($"{min:0}", new Font("Arial", 8), Brushes.Blue, x, y + legendHeight);
            g.DrawString($"{max:0}", new Font("Arial", 8), Brushes.Red, x + legendWidth - 24, y + legendHeight);
        }
    }

    class HeatmapGenerator
    {
        public static void Generate(List<DataEntry> data, string outputPath)
        {
            // 参数配置
            const int cellSize = 2;    // 每个数据点占2x2像素
            const int padding = 20;    // 画布边距
            const int legendHeight = 40; // 为图例预留的高度

            // 获取行列实际范围
            int minRow = data.Min(e => e.Row);
            int maxRow = data.Max(e => e.Row);
            int minCol = data.Min(e => e.Col);
            int maxCol = data.Max(e => e.Col);

            // 计算实际行列数量
            int rowCount = maxRow - minRow + 1;
            int colCount = maxCol - minCol + 1;

            // 计算画布尺寸（自动限制最大4096）
            int canvasWidth = Math.Min(colCount * cellSize + padding * 2, 4096);
            int canvasHeight = Math.Min(rowCount * cellSize + padding * 2 + legendHeight, 4096);

            // 内容区域居中坐标
            int contentWidth = colCount * cellSize;
            int contentHeight = rowCount * cellSize;
            int startX = (canvasWidth - contentWidth) / 2;
            int startY = (canvasHeight - contentHeight) / 2;

            // 计算值范围
            double minValue = data.Min(e => e.Value) * 1000;
            double maxValue = data.Max(e => e.Value) * 1000;

            using (var bmp = new Bitmap(canvasWidth, canvasHeight))
            using (var g = Graphics.FromImage(bmp))
            {
                g.Clear(Color.White);

                // 批量绘制优化
                foreach (var entry in data)
                {
                    // 转换为相对坐标
                    int relativeX = (entry.Col - minCol) * cellSize;
                    int relativeY = (entry.Row - minRow) * cellSize;

                    // 计算实际坐标
                    int x = startX + relativeX;
                    int y = startY + relativeY;

                    // 获取颜色
                    Color color = GetColorFromGradient(entry.Value * 1000, minValue, maxValue);

                    // 绘制矩形区域
                    g.FillRectangle(new SolidBrush(color), x, y, cellSize, cellSize);
                }

                // 绘制颜色图例
                int legendY = canvasHeight - legendHeight;
                int legendBarHeight = 15;
                DrawColorLegend(g, minValue, maxValue, padding, legendY, canvasWidth - padding * 2, legendBarHeight);

                bmp.Save(outputPath);
            }
        }

        public static void Generate(List<DataEntry1> data, string outputPath)
        {
            // 参数配置
            const int cellSize = 2;    // 每个数据点占2x2像素
            const int padding = 20;    // 画布边距
            const int legendHeight = 40; // 为图例预留的高度

            // 获取行列实际范围
            int minRow = data.Min(e => e.Row);
            int maxRow = data.Max(e => e.Row);
            int minCol = data.Min(e => e.Col);
            int maxCol = data.Max(e => e.Col);

            // 计算实际行列数量
            int rowCount = maxRow - minRow + 1;
            int colCount = maxCol - minCol + 1;

            // 计算画布尺寸（自动限制最大4096）
            int canvasWidth = Math.Min(colCount * cellSize + padding * 2, 4096);
            int canvasHeight = Math.Min(rowCount * cellSize + padding * 2 + legendHeight, 4096);

            // 内容区域居中坐标
            int contentWidth = colCount * cellSize;
            int contentHeight = rowCount * cellSize;
            int startX = (canvasWidth - contentWidth) / 2;
            int startY = (canvasHeight - contentHeight) / 2;

            // 计算值范围
            double minValue = data.Min(e => e.Value) * 1000;
            double maxValue = data.Max(e => e.Value) * 1000;

            using (var bmp = new Bitmap(canvasWidth, canvasHeight))
            using (var g = Graphics.FromImage(bmp))
            {
                g.Clear(Color.White);

                // 批量绘制优化
                foreach (var entry in data)
                {
                    // 转换为相对坐标
                    int relativeX = (entry.Col - minCol) * cellSize;
                    int relativeY = (entry.Row - minRow) * cellSize;

                    // 计算实际坐标
                    int x = startX + relativeX;
                    int y = startY + relativeY;

                    // 获取颜色
                    Color color = GetColorFromGradient(entry.Value * 1000, minValue, maxValue);

                    // 绘制矩形区域
                    g.FillRectangle(new SolidBrush(color), x, y, cellSize, cellSize);
                }

                // 绘制颜色图例
                int legendY = canvasHeight - legendHeight;
                int legendBarHeight = 15;
                DrawColorLegend(g, minValue, maxValue, padding, legendY, canvasWidth - padding * 2, legendBarHeight);

                bmp.Save(outputPath);
            }
        }

        private static Color GetColorFromGradient(double value, double min, double max)
        {
            double normalizedValue = (value - min) / (max - min);
            // 改进的颜色渐变，使用热图方案
            if (normalizedValue < 0.25)
            {
                // 蓝色到青色
                int b = 255;
                int g = (int)(255 * (normalizedValue * 4));
                return Color.FromArgb(0, g, b);
            }
            else if (normalizedValue < 0.5)
            {
                // 青色到绿色
                int b = (int)(255 * (1 - (normalizedValue - 0.25) * 4));
                return Color.FromArgb(0, 255, b);
            }
            else if (normalizedValue < 0.75)
            {
                // 绿色到黄色
                int r = (int)(255 * ((normalizedValue - 0.5) * 4));
                return Color.FromArgb(r, 255, 0);
            }
            else
            {
                // 黄色到红色
                int g = (int)(255 * (1 - (normalizedValue - 0.75) * 4));
                return Color.FromArgb(255, g, 0);
            }
        }

        private static void DrawColorLegend(Graphics g, double min, double max, int x, int y, int width, int height)
        {
            // 绘制渐变条
            for (int i = 0; i < width; i++)
            {
                // 根据位置计算对应的值
                double value = min + (max - min) * i / (double)width;
                Color color = GetColorFromGradient(value, min, max);
                using (var pen = new Pen(color))
                {
                    g.DrawLine(pen, x + i, y, x + i, y + height);
                }
            }

            // 绘制边框
            g.DrawRectangle(Pens.Black, x, y, width, height);

            // 绘制标签
            using (var font = new Font("Arial", 10))
            using (var brush = new SolidBrush(Color.Black))
            {
                string minText = $"{min:F2}";
                string maxText = $"{max:F2}";
                g.DrawString(minText, font, brush, x, y + height + 2);
                g.DrawString(maxText, font, brush, x + width - g.MeasureString(maxText, font).Width, y + height + 2);
            }
        }
    }
}