# Decision Log

This file records architectural and implementation decisions using a list format.
2025-07-03 09:26:41 - Log of updates made.

*
      
## Decision

*
      
## Rationale 

*

## Implementation Details

*
- **2025-07-03 09:29:32**: `msbuild` command failed. Switching strategy to use `dotnet build` as it's a more modern tool and more likely to be in the system's PATH.
[2025-07-03 09:37:38] - DevOps: Switched from msbuild to 'dotnet build' for compiling the solution based on user feedback.

---
### Decision (Code)
[2025-07-03 09:54:02] - 实现了基于输入SN数量的动态计算模式切换逻辑。

**Rationale:**
根据 `computation_mode_spec.md` 的要求，应用需要支持单棱镜和双棱镜两种计算模式。通过在UI事件处理层（`next_step_button_Click`）中添加逻辑来解析用户输入的SN列表，并根据列表的长度和组合来动态选择执行路径，可以满足此功能需求，同时将核心计算逻辑与UI判断分离开。

**Details:**
- **文件**: [`Entrance.cs`](Entrance.cs:323)
- **方法**: `next_step_button_Click`
- **核心逻辑**:
  - 添加了 `ParseSnInput` 方法来处理来自UI文本框的、以逗号或换行符分隔的SN字符串。
  - `next_step_button_Click` 方法被重构，以容纳单棱镜、双棱镜和错误状态的判断分支。
  - 文件保存路径现在根据计算模式动态生成（例如 `./sn/` 或 `./bigSn_smallSn/`）。
- 2025-07-03 10:03:35 - Switched to `dotnet build` for compiling the solution `prism_bond_analysis.sln` as per user instruction.
[2025-07-03 10:17:45] - Switched from `msbuild` to `dotnet build` for solution compilation based on user feedback.
[2025-07-03 10:30:15] - Deployment paused. User has requested a new feature: Save data and images for "Big SN" and "Small SN" into separate, respective folders. This requires code changes.

---
### Decision (Code)
[2025-07-03 10:31:57] - 为大/小SN和配对计算实现独立的输出文件夹。

**Rationale:**
根据用户的新功能需求，需要将不同计算模式的输出文件（数据和图像）保存到各自专用的文件夹中，以改善文件管理的结构性和清晰度。

**Details:**
- **文件**: [`Entrance.cs`](Entrance.cs)
- **方法**: `ProcessSinglePrism`, `btnCalculate_Click`
- **核心逻辑**:
  - 在 `ProcessSinglePrism` 方法中，根据棱镜类型（"big" 或 "small"）动态构建基础目录路径 (`data_result/BigSN/` 或 `data_result/SmallSN/`)。
  - 在 `btnCalculate_Click` 方法中，为双棱镜配对模式指定了新的基础目录 (`data_result/Paired/`)。

---
### Decision (Code)
[2025-07-03 10:38:49] - 在双棱镜配对流程中，为大、小棱镜分别创建独立的输出文件夹。

**
Rationale:**
用户要求在执行双棱镜配对计算时，除了保存最终的配对数据外，还需要保存每个棱镜（大棱镜和小棱镜）各自独立的计算结果。通过为每个SN创建单独的文件夹，可以确保数据的模块化和可追溯性，便于单独分析或调试，同时保持配对结果的完整性。

**Details:**
- **文件**: [`Entrance.cs`](Entrance.cs)
- **方法**: `btnCalculate_Click`
- **核心逻辑**:
  - 在双棱镜配对的循环中，为大棱镜 (`data_result/BigSN/{bigSn}/`) 和小棱镜 (`data_result/SmallSN/{smallSn}/`) 分别定义并创建了独立的保存目录。
  - 在处理完大棱镜后，调用 `AppendOrCreateCsv` 方法，将 `smallSn` 参数设为 `null`，以便只保存大棱镜的数据到其独立目录中。
  - 在处理完小棱镜后，同样调用 `AppendOrCreateCsv` 方法，将 `bigSn` 参数设为 `null`，以保存小棱镜的数据。
  - 最终的配对数据和包含所有信息的组合CSV文件仍然保存在 `data_result/Paired/{bigSn}_{smallSn}/` 目录中。

---
### Decision (Code)
[2025-07-03 10:45:41] - 为单棱镜计算实现分区块功能。

**Rationale:**
根据用户需求，单棱镜计算流程需要与双棱镜配对后的计算流程保持一致，对拟合面进行分区块并独立计算每个区块的指标。通过重构 `ProcessSinglePrism` 方法，直接调用 `CalcPV.cs` 中已有的分区块计算函数 (`SplitCollection_a`, `_b`, `_c`) 和其他核心计算函数，可以实现此功能，确保了两种模式下数据处理逻辑的一致性和代码的重用性。

**Details:**
- **文件**: [`Entrance.cs`](Entrance.cs)
- **方法**: `ProcessSinglePrism`
- **核心逻辑**:
  - 移除了对 `CalcPV.ProcessMatrices` 的调用，因为它主要用于双棱镜配对。
  - 在从文件读取数据后，直接依次调用 `CalcPV.SplitCollection_c`, `CalcPV.SplitCollection_a`, `CalcPV.SplitCollection_b` 对数据点集进行分区。
  - 随后，调用 `CalcPV.CalculatePV99`, `CalcPV.CalculateGradients`, 和 `CalcPV.minVal` 等函数来计算和保存完整的及分区块的指标。
  - 更新了 `AppendOrCreateCsv` 的调用逻辑，以正确处理大棱镜和小棱镜的SN。

---
### Decision (Code)
[2025-07-03 12:21:00] - Implemented `BackgroundWorker` to resolve UI freezing during calculation.

**Rationale:**
The original implementation performed a long-running, computationally intensive task directly in the `btnCalculate_Click` event handler. This blocked the UI thread, making the application unresponsive. The `System.ComponentModel.BackgroundWorker` provides a straightforward, event-based model for offloading this work to a separate thread, while safely reporting progress and marshalling results back to the UI thread. This was the most direct and appropriate solution within the existing WinForms framework.

**Details:**
- **File:** `Entrance.cs`
- **Methods Added:** `backgroundWorker1_DoWork`, `backgroundWorker1_ProgressChanged`, `backgroundWorker1_RunWorkerCompleted`
- **Method Refactored:** `btnCalculate_Click`