# Decision Log

This file records architectural and implementation decisions using a list format.
2025-07-03 09:26:41 - Log of updates made.

*
      
## Decision

*
      
## Rationale 

*

## Implementation Details

*
- **2025-07-03 09:29:32**: `msbuild` command failed. Switching strategy to use `dotnet build` as it's a more modern tool and more likely to be in the system's PATH.
[2025-07-03 09:37:38] - DevOps: Switched from msbuild to 'dotnet build' for compiling the solution based on user feedback.

---
### Decision (Code)
[2025-07-03 09:54:02] - 实现了基于输入SN数量的动态计算模式切换逻辑。

**Rationale:**
根据 `computation_mode_spec.md` 的要求，应用需要支持单棱镜和双棱镜两种计算模式。通过在UI事件处理层（`next_step_button_Click`）中添加逻辑来解析用户输入的SN列表，并根据列表的长度和组合来动态选择执行路径，可以满足此功能需求，同时将核心计算逻辑与UI判断分离开。

**Details:**
- **文件**: [`Entrance.cs`](Entrance.cs:323)
- **方法**: `next_step_button_Click`
- **核心逻辑**:
  - 添加了 `ParseSnInput` 方法来处理来自UI文本框的、以逗号或换行符分隔的SN字符串。
  - `next_step_button_Click` 方法被重构，以容纳单棱镜、双棱镜和错误状态的判断分支。
  - 文件保存路径现在根据计算模式动态生成（例如 `./sn/` 或 `./bigSn_smallSn/`）。
- 2025-07-03 10:03:35 - Switched to `dotnet build` for compiling the solution `prism_bond_analysis.sln` as per user instruction.
[2025-07-03 10:17:45] - Switched from `msbuild` to `dotnet build` for solution compilation based on user feedback.
[2025-07-03 10:30:15] - Deployment paused. User has requested a new feature: Save data and images for "Big SN" and "Small SN" into separate, respective folders. This requires code changes.

---
### Decision (Code)
[2025-07-03 10:31:57] - 为大/小SN和配对计算实现独立的输出文件夹。

**Rationale:**
根据用户的新功能需求，需要将不同计算模式的输出文件（数据和图像）保存到各自专用的文件夹中，以改善文件管理的结构性和清晰度。

**Details:**
- **文件**: [`Entrance.cs`](Entrance.cs)
- **方法**: `ProcessSinglePrism`, `btnCalculate_Click`
- **核心逻辑**:
  - 在 `ProcessSinglePrism` 方法中，根据棱镜类型（"big" 或 "small"）动态构建基础目录路径 (`data_result/BigSN/` 或 `data_result/SmallSN/`)。
  - 在 `btnCalculate_Click` 方法中，为双棱镜配对模式指定了新的基础目录 (`data_result/Paired/`)。

---
### Decision (Code)
[2025-07-03 10:38:49] - 在双棱镜配对流程中，为大、小棱镜分别创建独立的输出文件夹。

**
Rationale:**
用户要求在执行双棱镜配对计算时，除了保存最终的配对数据外，还需要保存每个棱镜（大棱镜和小棱镜）各自独立的计算结果。通过为每个SN创建单独的文件夹，可以确保数据的模块化和可追溯性，便于单独分析或调试，同时保持配对结果的完整性。

**Details:**
- **文件**: [`Entrance.cs`](Entrance.cs)
- **方法**: `btnCalculate_Click`
- **核心逻辑**:
  - 在双棱镜配对的循环中，为大棱镜 (`data_result/BigSN/{bigSn}/`) 和小棱镜 (`data_result/SmallSN/{smallSn}/`) 分别定义并创建了独立的保存目录。
  - 在处理完大棱镜后，调用 `AppendOrCreateCsv` 方法，将 `smallSn` 参数设为 `null`，以便只保存大棱镜的数据到其独立目录中。
  - 在处理完小棱镜后，同样调用 `AppendOrCreateCsv` 方法，将 `bigSn` 参数设为 `null`，以保存小棱镜的数据。
  - 最终的配对数据和包含所有信息的组合CSV文件仍然保存在 `data_result/Paired/{bigSn}_{smallSn}/` 目录中。

---
### Decision (Code)
[2025-07-03 10:45:41] - 为单棱镜计算实现分区块功能。

**Rationale:**
根据用户需求，单棱镜计算流程需要与双棱镜配对后的计算流程保持一致，对拟合面进行分区块并独立计算每个区块的指标。通过重构 `ProcessSinglePrism` 方法，直接调用 `CalcPV.cs` 中已有的分区块计算函数 (`SplitCollection_a`, `_b`, `_c`) 和其他核心计算函数，可以实现此功能，确保了两种模式下数据处理逻辑的一致性和代码的重用性。

**Details:**
- **文件**: [`Entrance.cs`](Entrance.cs)
- **方法**: `ProcessSinglePrism`
- **核心逻辑**:
  - 移除了对 `CalcPV.ProcessMatrices` 的调用，因为它主要用于双棱镜配对。
  - 在从文件读取数据后，直接依次调用 `CalcPV.SplitCollection_c`, `CalcPV.SplitCollection_a`, `CalcPV.SplitCollection_b` 对数据点集进行分区。
  - 随后，调用 `CalcPV.CalculatePV99`, `CalcPV.CalculateGradients`, 和 `CalcPV.minVal` 等函数来计算和保存完整的及分区块的指标。
  - 更新了 `AppendOrCreateCsv` 的调用逻辑，以正确处理大棱镜和小棱镜的SN。

---
### Decision (Code)
[2025-07-03 12:21:00] - Implemented `BackgroundWorker` to resolve UI freezing during calculation.

**Rationale:**
The original implementation performed a long-running, computationally intensive task directly in the `btnCalculate_Click` event handler. This blocked the UI thread, making the application unresponsive. The `System.ComponentModel.BackgroundWorker` provides a straightforward, event-based model for offloading this work to a separate thread, while safely reporting progress and marshalling results back to the UI thread. This was the most direct and appropriate solution within the existing WinForms framework.

**Details:**
- **File:** `Entrance.cs`
- **Methods Added:** `backgroundWorker1_DoWork`, `backgroundWorker1_ProgressChanged`, `backgroundWorker1_RunWorkerCompleted`
- **Method Refactored:** `btnCalculate_Click`
---
**Decision Log Entry: 2025-07-03 13:08:34**

**Context:** User requested a new feature to generate a high-resolution (1920x1080) image with detailed annotations based on the calculation results from `CalcPV.cs`.

**Decision:**
1.  **Create a New Module:** A new class, `AdvancedImageGenerator`, will be created to handle the image generation. This isolates the complex drawing logic from the calculation logic in `CalcPV.cs`.
2.  **Leverage Existing Data:** The generator will take the final `List<DataEntry>` and the `GlobalData` object as input. It will not perform any new calculations but will focus solely on visualizing the pre-calculated metrics (regional PV, min/max points, gradients).
3.  **Core Features:** The generator will be responsible for:
    *   Creating a 1920x1080 canvas.
    *   Mapping data coordinates to image coordinates with proper scaling and centering.
    *   Drawing a base heatmap.
    *   Drawing dashed lines to delineate the A, B, and C analysis regions.
    *   Labeling each region.
    *   Marking min/max points and max gradient points with clear visual indicators (symbols, labels, arrows).
4.  **Integration:** The new `AdvancedImageGenerator.Generate()` method will be called after all calculations are complete in the main workflow (likely within `Entrance.cs` or `CalcPV.cs`).

**Reasoning:**
*   **Modularity:** Separating image generation from data calculation improves code organization and maintainability. `CalcPV.cs` remains focused on computation, while `AdvancedImageGenerator` handles all presentation logic.
*   **Efficiency:** By using the already computed `GlobalData`, we avoid redundant calculations. The new feature is purely for visualization.
*   **Clarity:** The proposed annotations directly address all user requirements, providing a comprehensive visual summary of the analysis on a single, high-resolution image.

**Impact:**
*   A new file, `AdvancedImageGenerator.cs`, will be created.
*   The calling code in `Entrance.cs` or `CalcPV.cs` will need to be modified to invoke the new generator.
*   The existing `HeatmapGenerator` may be deprecated or integrated into the new module.
*   `productContext.md` has been updated to include this new feature.

---
### Decision (Architecture)
[2025-07-03 13:12:00] - 设计并集成了高级图像生成模块 `AdvancedImageGenerator`。

**Rationale:**
为了满足生成带标注的高分辨率分析图像的需求，同时保持代码的模块化和可维护性，决定创建一个独立的图像生成模块。此模块将计算逻辑 (`CalcPV.cs`) 与可视化逻辑分离。使用DTO (`CalculationMetrics`) 和快照方法 (`GlobalData.GetMetricsSnapshot()`) 来确保在多线程环境（`BackgroundWorker`）中安全地传递数据，避免了直接访问全局静态变量可能导致的线程安全问题。

**Details:**
- **新文件**: `AdvancedImageGenerator.cs` 已创建并放置在项目根目录。
  - **`CalculationMetrics`**: 定义了一个DTO，用于捕获`GlobalData`中计算结果的快照。
  - **`ImageGenerationData`**: 定义了一个数据结构，用于封装生成图像所需的所有输入。
  - **`AdvancedImageGenerator`**: 一个静态类，包含`Generate`方法，负责在1920x1080的画布上绘制热力图、区域边界和注解。
- **修改的文件**:
  - **`Configure.cs`**: 在`GlobalData`类中添加了`GetMetricsSnapshot()`方法，以创建`CalculationMetrics`的线程安全实例。
  - **`Entrance.cs`**: 在`backgroundWorker1_DoWork`方法中，于单棱镜和双棱镜计算成功后，调用`AdvancedImageGenerator.Generate()`来生成并保存图像。
- **集成点**:
  - 在`ProcessSinglePrism`方法成功处理单个棱镜后调用。
  - 在`backgroundWorker1_DoWork`中，当双棱镜配对计算`ProcessMatrices`成功后调用。
---
### Decision (Debug)
[2025-07-03 14:39:30] - [Fix C# Build Errors by Consolidating Types and Updating Project File]

**Rationale:**
The initial build failed due to several `CS0246` (type not found) and `CS0103` (name does not exist in context) errors. The root causes were:
1.  Newly introduced classes (`CalculationMetrics`, `ImageGenerationData`) were not accessible from where they were being used.
2.  A new source file (`AdvancedImageGenerator.cs`) was not included in the C# project file (`.csproj`), so the compiler was ignoring it entirely.
3.  A logging method was called with an incorrect name (`WriteWarning` instead of `WriteWarn`).

The chosen strategy was to systematically address each compilation error. First, by moving the shared DTO classes (`CalculationMetrics`, `ImageGenerationData`) to a central, already-included file (`Configure.cs`). Second, by adding the missing source file to the `.csproj` file's `Compile` item group. Finally, by correcting the typo in the logging method call. This approach ensures all types are visible and all necessary files are included in the build.

**Details:**
- **Affected components/files:**
  - `prism_bond_analysis.csproj`: Added `<Compile Include="AdvancedImageGenerator.cs" />`.
  - `Configure.cs`: Moved `CalculationMetrics` and `ImageGenerationData` class definitions here. Added `using System.Drawing;`.
  - `AdvancedImageGenerator.cs`: Corrected `Logs.WriteWarning` to `Logs.WriteWarn`. Removed DTO class definitions.
  - `Entrance.cs`: Errors here were resolved by the above changes.