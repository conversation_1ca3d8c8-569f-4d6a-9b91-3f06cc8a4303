﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Nreal_ProductLine_Tool.onlyScan
{
    internal static class Program
    {
        /// <summary>
        /// 应用程序的主入口点。
        /// </summary>
        [STAThread]
        static void Main(string[] args)
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);
            if (args.Length == 10)
            {
                LOGINFO.mode = args[0];
                LOGINFO.user = args[1];
                LOGINFO.rname = args[2];
                LOGINFO.lname = args[3];
                LOGINFO.pname = args[4];
                LOGINFO.configMode = args[5];
                LOGINFO.dbWrite = args[6];
                LOGINFO.mesEnable = args[7];
                LOGINFO.pass = args[8];
                Application.Run(new Entrance());
            }
            if (args.Length == 0)
            {
                //prism_bond.Program.test();
                //RectangleExtraction.Program.test();
                
                LOGINFO.user = "test";
                LOGINFO.mode = "offline";
                LOGINFO.configMode = "local";
                LOGINFO.dbWrite = "false";
                LOGINFO.mesEnable = "false";
                LOGINFO.scan = "false";
                Application.Run(new Entrance());
            }
        }
    }

    public class LOGINFO
    {
        public static string mode = "";
        public static string token = "default";
        public static string user = "";
        public static string rname = "default";
        public static string lname = "default";
        public static string pname = "default";
        public static string configMode = "net";  // net local
        public static string dbWrite = "true";
        public static string mesEnable = "true";
        public static string pass = "";
        public static string reserved2 = "";
        public static string scan = "true";
    }

    public class Context
    {
        public string SN { get; set; }
        public string S_SN { get; set; }
        public double GWeight { get; set; }
    }

    public class CameraImageLib
    {
        [DllImport("opencv.dll", CallingConvention = CallingConvention.Cdecl)]
        public static extern int imageHandle([MarshalAs(UnmanagedType.LPStr)] string name);
        [DllImport("BaslerController.dll", CallingConvention = CallingConvention.Cdecl)]
        public static extern int Init_pb(int[] para);
        [DllImport("BaslerController.dll", CallingConvention = CallingConvention.Cdecl)]
        public static extern int Capture();
        [DllImport("BaslerController.dll", CallingConvention = CallingConvention.Cdecl)]
        public static extern int AnalysisPB(int type, double[] result, int status);
        [DllImport("BaslerController.dll", CallingConvention = CallingConvention.Cdecl)]
        public static extern int RegisterCallBackShowImage(Delegate image_transfer);
        [DllImport("BaslerController.dll", CallingConvention = CallingConvention.Cdecl)]
        public static extern int ShowImage();
        [DllImport("BaslerController.dll", CallingConvention = CallingConvention.Cdecl)]
        public static extern void SetExposureTime(double value);
        [DllImport("BaslerController.dll", CallingConvention = CallingConvention.Cdecl)]
        public static extern void SetGain(double value);
        [DllImport("BaslerController.dll", CallingConvention = CallingConvention.Cdecl)]
        public static extern void SetParamD([MarshalAs(UnmanagedType.LPStr)] string key, double value);
        [DllImport("BaslerController.dll", CallingConvention = CallingConvention.Cdecl)]
        public static extern void SetPicPath([MarshalAs(UnmanagedType.LPStr)] string name);
        [DllImport("BaslerController.dll", CallingConvention = CallingConvention.Cdecl)]
        public static extern void SetParamS([MarshalAs(UnmanagedType.LPStr)] string key, [MarshalAs(UnmanagedType.LPStr)] string value);

    }
}
