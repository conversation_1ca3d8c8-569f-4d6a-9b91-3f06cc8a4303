﻿using ParamManager;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using System.Web.UI.WebControls;
using System.Windows.Forms;
using static System.Net.Mime.MediaTypeNames;
using static System.Windows.Forms.VisualStyles.VisualStyleElement;
using System.IO.Compression;
using System.Reflection.Emit;
using System.Text.RegularExpressions;
using System.Runtime.InteropServices;
using TextBox = System.Windows.Forms.TextBox;
using System.Xml.Linq;
using System.Security.Cryptography;

namespace Nreal_ProductLine_Tool.onlyScan
{    
    public partial class Entrance : Form
    {
        int currentIndex = 0;
        int toujing_scan_sequence = 0;
        int MotionZIndex = 0;
        int ColimatorHandleIndex = 0;
        FormDefault preCheck;
        int mesEnable = 0;
        bool isExited = false;
        bool isStopCapture = false;
        string snRemote;  // 扫码程序传过来的sn码
        string snPrism;
        string resMes; // 扫码程序传过来的mes资源
        byte[] resultBuf = new byte[128];
        Dictionary<string, string> mes_resource = new Dictionary<string, string>();
        List<FormDefault> forms = new List<FormDefault>();
        DataSet dataSet = new DataSet();
        string fileSaver;

        double ud = 0, lr = 0;
        double min_val = 0;
        int row = 0, col = 0;
        double ud_pv = 0, lr_pv = 0;
        double gradient = 0;
        double direction = 0;
        double d_pv = 0;
        double x_pv = 0;
        double y_pv = 0;

        public Entrance()
        {
            InitializeComponent();
            this.Load += MainForm_Load;
            this.FormClosed += MainForm_FormClosed;            
        }

        private void MainForm_Load(object sender, EventArgs e)
        {
            //prism_bond.Program.ConvertFormat("10.xyz", "10.txt");

            Logs log = new Logs();
            Logs.WriteInfo("################ start ################", true);
            if (!Directory.Exists("Data"))
            {
                Directory.CreateDirectory("Data");
            }

            int ret = XrPLCom.xrCommInit(1, 0, Encoding.UTF8.GetBytes(LOGINFO.user));
            if (ret != 0)
            {
                MessageBox.Show("请连接网络");
                Process.GetCurrentProcess().Kill();
            }

            ret = Configure.Init_Local();
            if (ret != 0)
            {
                MessageBox.Show("请检查本地配置文件 sys.ini ");
                Process.GetCurrentProcess().Kill();
            }

            FtpHelper.FtpSet("172.50.10.36", "xreal", "123456");
            FtpHelper1.FtpSet("172.20.96.19", "BiUser", "o41Gkwvo4WRVIKKP");

            loadHoleInfo();

            foreach (DataRow row in dataSet.Tables[0].Rows)
            {
                string hidValue = row["HOLD_ID"].ToString();
                if (hidValue.StartsWith("D"))
                {
                    comboBox1.Items.Add("大" + hidValue.Substring(1));
                }
                if (hidValue.StartsWith("X"))
                {
                    comboBox2.Items.Add("小" + hidValue.Substring(1));
                }
            }

            string directoryPath = @"data1"; // 替换为实际路径

            try
            {
                // 检查文件夹是否存在
                if (!Directory.Exists(directoryPath))
                {
                    // 创建文件夹
                    Directory.CreateDirectory(directoryPath);
                    Console.WriteLine($"文件夹已创建: {directoryPath}");
                }
                else
                {
                    Console.WriteLine($"文件夹已存在: {directoryPath}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"处理文件夹时出错: {ex.Message}");
            }

        }

        private void MainForm_FormClosed(object sender, FormClosedEventArgs e)
        {
            System.Environment.Exit(0);
        }

        public void errMsgShow(string message)
        {
            err_label.BackColor = Color.Red;
            err_label.Text = message;
        }

        public void errMsgClear()
        {
            err_label.BackColor = Color.Gray;
            err_label.Text = "";
        }

        private void loadHoleInfo()
        {
            errMsgClear();
            string searchStr = "SELECT DISTINCT HOLD_ID FROM g_prism_data";
            int ret1 = XrPLCom.GetGDbData(searchStr, ref dataSet);
            if (ret1 != 0 || dataSet.Tables[0].Rows.Count == 0)
            {
                Logs.WriteDebug("读取模穴号信息失败", true);
                errMsgShow("读取模穴号信息失败");
                return;
            }
        }

        int GetFileName(string sn, ref string name, int type)
        {
            string searchStr = "SELECT ID ,SN, SN_Time FROM g_prism_data WHERE SN='" + sn + "' ORDER BY ID DESC limit 1";
            DataSet dataResult = new DataSet();
            int ret1 = XrPLCom.GetGDbData(searchStr, ref dataResult);
            if (ret1 != 0 || dataResult.Tables[0].Rows.Count == 0)
            {
                Logs.WriteDebug("读取数据库查找文件失败: " + sn, true);
                errMsgShow("读取数据库查找棱镜文件失败");
                return -1;
            }

            string SN_Time = dataResult.Tables[0].Rows[0]["SN_Time"].ToString();
            if (SN_Time == "")
            {
                name = sn;
                return 1;
            }
            else
                name = SN_Time;

            return 2;
        }

        private int pv_handle(string sn, int flag)
        {
            errMsgClear();
            if (flag == 0)
                GlobalData.big_prism_fname = "";
            else
                GlobalData.small_prism_fname = "";
            string big_name = "";

            
            int ret = GetFileName(sn, ref big_name, 0);
            if (ret < 0)
                return -1;

            string filePath = "Data/" + big_name;
            if (File.Exists(filePath))
            {
                if (flag == 0)
                    GlobalData.big_prism_fname = "Data/" + big_name;
                else
                    GlobalData.small_prism_fname = "Data/" + big_name;
            }
            else
            {
                bool downloadResult = false;
                if (ret == 1)
                {
                    big_name = big_name + ".txt";
                    downloadResult = FtpHelper.DownloadFile("prism_bond\\parsed_data\\" + big_name, "Data/" + big_name);
                }
                else
                    downloadResult = FtpHelper1.DownloadFile("手动线\\一线\\大小棱镜面型数据\\parsed_data\\" + big_name, "Data/" + big_name);

                if (downloadResult)
                {
                    if (flag == 0)
                        GlobalData.big_prism_fname = "Data/" + big_name;
                    else
                        GlobalData.small_prism_fname = "Data/" + big_name;
                }
                else
                {
                    MessageBox.Show("文件不存在,请检查是否上传大棱镜面型数据");
                    return -1;
                }
            }

            List<DataEntry> entries1 = CalcPV.ReadDataFromFile("Data/" + big_name);

            CalcPV.CalculatePV99(ref entries1, ref GlobalData.d_pv, ref GlobalData.d_pv99);
            if (flag == 0)
            {
            }
            else
            {
            }

            return 0;
        }

        private void txtBigPrismSN_KeyUp(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Control || e.KeyCode == Keys.Enter)
            {
                D_status_label.Text = "";
                string sn = txtBigPrismSN.Text;

                if (sn.Length != 10)
                {
                    D_status_label.Text = "sn长度不对，当前sn为" + sn;
                    txtBigPrismSN.Text = "";
                    return;
                }

                txtBigPrismSN.Text = "";

                int ret = pv_handle(sn, 0);
                if (ret != 0)
                    return;
                D_status_label.Text = sn;
            }
        }

        private void txtSmallPrismSN_KeyUp(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Control || e.KeyCode == Keys.Enter)
            {
                X_status_label.Text = "";
                string sn = txtSmallPrismSN.Text;

                if (sn.Length != 10)
                {
                    X_status_label.Text = "sn长度不对，当前sn为" + sn;
                    txtSmallPrismSN.Text = "";
                    return;
                }

                txtSmallPrismSN.Text = "";

                int ret = pv_handle(sn, 1);
                if (ret != 0)
                    return;
                X_status_label.Text = sn;
            }
        }

        private void txtBigPrismSN_MouseEnter(object sender, EventArgs e)
        {
            TextBox textBox = (TextBox)sender;
            IntPtr hWnd = textBox.Handle;
            IntPtr hIMC = ImmGetContext(hWnd);

            if (hIMC != IntPtr.Zero)
            {
                // 设置为半角（0x0001为半角，0x0002为全角）
                ImmSetConversionStatus(hIMC, 0x0001, 0);
                ImmReleaseContext(hWnd, hIMC);
            }
        }

        private void txtSmallPrismSN_MouseEnter(object sender, EventArgs e)
        {
            TextBox textBox = (TextBox)sender;
            IntPtr hWnd = textBox.Handle;
            IntPtr hIMC = ImmGetContext(hWnd);

            if (hIMC != IntPtr.Zero)
            {
                // 设置为半角（0x0001为半角，0x0002为全角）
                ImmSetConversionStatus(hIMC, 0x0001, 0);
                ImmReleaseContext(hWnd, hIMC);
            }
        }

        private void btnCalculate_Click(object sender, EventArgs e)
        {
            errMsgClear();

            var bigPrismSNs = ParseSnInput(txtBigPrismSN.Text);
            var smallPrismSNs = ParseSnInput(txtSmallPrismSN.Text);

            if (bigPrismSNs.Count == 0 && smallPrismSNs.Count == 0)
            {
                MessageBox.Show("错误：请输入SN。", "输入错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            if (bigPrismSNs.Count > 0 && smallPrismSNs.Count > 0 && bigPrismSNs.Count != smallPrismSNs.Count)
            {
                MessageBox.Show("错误：大棱镜和小棱镜SN数量不匹配。", "输入错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            // 禁用按钮并显示进度条
            btnCalculate.Enabled = false;
            progressBar1.Value = 0;
            progressBar1.Visible = true;

            // 准备参数并启动BackgroundWorker
            var arguments = new Dictionary<string, List<string>>
            {
                { "bigPrismSNs", bigPrismSNs },
                { "smallPrismSNs", smallPrismSNs }
            };
            backgroundWorker1.RunWorkerAsync(arguments);
        }

        /// <summary>
        /// Processes a single prism, saves its result files, and writes data to CSV.
        /// </summary>
        /// <param name="sn">The Serial Number of the prism.</param>
        /// <param name="prismType">"big" or "small".</param>
        /// <returns>True if successful, otherwise false.</returns>
        private bool ProcessSinglePrism(string sn, string prismType)
        {
            string baseDir = (prismType == "big") ? "data_result/BigSN" : "data_result/SmallSN";
            string saveDir = $"{baseDir}/{sn}/";
            Directory.CreateDirectory(saveDir);

            int prismFlag = (prismType == "big") ? 0 : 1;
            if (pv_handle(sn, prismFlag) != 0)
            {
                Logs.WriteError($"单棱镜计算失败，无法处理SN: {sn}", true);
                return false;
            }

            string dataFile = (prismFlag == 0) ? GlobalData.big_prism_fname : GlobalData.small_prism_fname;
            List<DataEntry> entries = CalcPV.ReadDataFromFile(dataFile);

            string surfaceMapPath = $"{saveDir}{sn}.png";
            string gradientMapPath = $"{saveDir}{sn}_梯度.png";

            // 设置全局SN信息，用于梯度图保存
            GlobalData.big_prism_sn = (prismType == "big") ? sn : null;
            GlobalData.small_prism_sn = (prismType == "small") ? sn : null;

            // 使用正确的单棱镜处理方法，它们会自动进行分区计算
            int result;
            if (prismType == "big")
            {
                result = CalcPV.ProcessSingleBigPrism(entries, surfaceMapPath, gradientMapPath);
            }
            else
            {
                result = CalcPV.ProcessSingleSmallPrism(entries, surfaceMapPath, gradientMapPath);
            }

            if (result != 0)
            {
                Logs.WriteError($"单棱镜 {sn} 计算失败", true);
                return false;
            }

            // Save CSV data using the new method to avoid data overwriting
            SaveIndividualCsvData((prismType == "big") ? sn : null, (prismType == "small") ? sn : null, saveDir);

            // Generate advanced annotated image
            try
            {
                var imageData = new ImageGenerationData
                {
                    AllPoints = entries,
                    Metrics = GlobalData.GetMetricsSnapshot(),
                    OutputPath = Path.Combine(saveDir, $"{sn}_annotated.png")
                };
                AdvancedImageGenerator.Generate(imageData);
            }
            catch (Exception ex)
            {
                Logs.WriteError($"为 {sn} 生成高级图像失败: {ex.Message}", true);
                // Do not fail the entire process, just log the error.
            }

            return true;
        }

        private List<string> ParseSnInput(string text)
        {
            if (string.IsNullOrWhiteSpace(text))
            {
                return new List<string>();
            }

            return text.Split(new[] { ',', '\n' }, StringSplitOptions.RemoveEmptyEntries)
                       .Select(sn => sn.Trim())
                       .Where(sn => !string.IsNullOrEmpty(sn))
                       .ToList();
        }

        static void AppendOrCreateCsv(string bigSn, string smallSn, string savePath)
        {
            string csvFile = Path.Combine(savePath, "data.csv");
            bool fileExists = File.Exists(csvFile);
            try
            {
                using (var writer = new StreamWriter(csvFile, append: true))
                {
                    if (!fileExists)
                    {
                        writer.WriteLine(GetCsvHeaders());
                    }
                    GlobalData.big_prism_sn = bigSn;
                    GlobalData.small_prism_sn = smallSn ?? ""; // 如果是单棱镜模式，smallSn为null
                    writer.WriteLine(GetCsvValues());
                }
            }
            catch (IOException ex)
            {
                MessageBox.Show(
                    $"写入文件时发生错误: {ex.Message}",
                    "文件操作错误",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error);
            }
        }

        public static string GetCsvHeaders()
        {
            return "big_sn, small_sn, pv99,gradiant_average,gradiant_max,gradiant_max_row,gradiant_max_col,gradiant_angle,max,min,max_row,max_col,min_row,min_col,"
                 + "a1_pv, a1_pv99, a2_pv,a2_pv99,a3_pv,a3_pv99,a4_pv,a4_pv99,a1_gradiant_average,a1_gradiant_max,a1_gradiant_max_row,a1_gradiant_max_col,"
                 + "a1_gradiant_angle, a2_gradiant_average, a2_gradiant_max,a2_gradiant_max_row,a2_gradiant_max_col,a2_gradiant_angle,a3_gradiant_average,"
                 + "a3_gradiant_max, a3_gradiant_max_row, a3_gradiant_max_col,a3_gradiant_angle,a4_gradiant_average,a4_gradiant_max,a4_gradiant_max_row,"
                 + "a4_gradiant_max_col, a4_gradiant_angle, a1_max,a1_min,a1_max_row,a1_max_col,a1_min_row,a1_min_col,a2_max,a2_min,a2_max_row,a2_max_col,"
                 + "a2_min_row, a2_min_col, a3_max,a3_min,a3_max_row,a3_max_col,a3_min_row,a3_min_col,a4_max,a4_min,a4_max_row,a4_max_col,a4_min_row,a4_min_col,"
                 + "b1_pv, b1_pv99,b2_pv,b2_pv99,b3_pv,b3_pv99,b4_pv,b4_pv99,b1_gradiant_average,b1_gradiant_max,b1_gradiant_max_row,b1_gradiant_max_col,"
                 + "b1_gradiant_angle, b2_gradiant_average, b2_gradiant_max,b2_gradiant_max_row,b2_gradiant_max_col,b2_gradiant_angle,b3_gradiant_average,"
                 + "b3_gradiant_max, b3_gradiant_max_row, b3_gradiant_max_col,b3_gradiant_angle,b4_gradiant_average,b4_gradiant_max,b4_gradiant_max_row,"
                 + "b4_gradiant_max_col, b4_gradiant_angle, b1_max,b1_min,b1_max_row,b1_max_col,b1_min_row,b1_min_col,b2_max,b2_min,b2_max_row,b2_max_col,"
                 + "b2_min_row, b2_min_col, b3_max,b3_min,b3_max_row,b3_max_col,b3_min_row,b3_min_col,b4_max,b4_min,b4_max_row,b4_max_col,b4_min_row,b4_min_col,"
                 + "c1_pv, c1_pv99, c2_pv,c2_pv99,c3_pv,c3_pv99,c4_pv,c4_pv99,c1_gradiant_average,c1_gradiant_max,c1_gradiant_max_row,c1_gradiant_max_col,"
                 + "c1_gradiant_angle, c2_gradiant_average, c2_gradiant_max,c2_gradiant_max_row,c2_gradiant_max_col,c2_gradiant_angle,c3_gradiant_average,"
                 + "c3_gradiant_max, c3_gradiant_max_row, c3_gradiant_max_col,c3_gradiant_angle,c4_gradiant_average,c4_gradiant_max,c4_gradiant_max_row,"
                 + "c4_gradiant_max_col, c4_gradiant_angle, c1_max,c1_min,c1_max_row,c1_max_col,c1_min_row,c1_min_col,c2_max,c2_min,c2_max_row,c2_max_col,"
                 + "c2_min_row, c2_min_col, c3_max,c3_min,c3_max_row,c3_max_col,c3_min_row,c3_min_col,c4_max,c4_min,c4_max_row,c4_max_col,c4_min_row,c4_min_col";
        }

        public static string GetCsvValues()
        {
            return $"{GlobalData.big_prism_sn}," +
                   $"{GlobalData.small_prism_sn}," +
                   $"{GlobalData.pv99_val}," +
                   $"{GlobalData.gradiant_average}," +
                   $"{GlobalData.gradiant_max}," +
                   $"{GlobalData.gradiant_max_row}," +
                   $"{GlobalData.gradiant_max_col}," +
                   $"{GlobalData.gradiant_angle}," +
                   $"{GlobalData.max}," +
                   $"{GlobalData.min}," +
                   $"{GlobalData.max_row}," +
                   $"{GlobalData.max_col}," +
                   $"{GlobalData.min_row}," +
                   $"{GlobalData.min_col},"+                   
                   $"{GlobalData.a1_pv}," +
                   $"{GlobalData.a1_pv99}," +
                   $"{GlobalData.a2_pv}," +
                   $"{GlobalData.a2_pv99}," +
                   $"{GlobalData.a3_pv}," +
                   $"{GlobalData.a3_pv99}," +
                   $"{GlobalData.a4_pv}," +
                   $"{GlobalData.a4_pv99}," +
                   $"{GlobalData.a1_gradiant_average}," +
                   $"{GlobalData.a1_gradiant_max}," +
                   $"{GlobalData.a1_gradiant_max_row}," +
                   $"{GlobalData.a1_gradiant_max_col}," +
                   $"{GlobalData.a1_gradiant_angle}," +
                   $"{GlobalData.a2_gradiant_average}," +
                   $"{GlobalData.a2_gradiant_max}," +
                   $"{GlobalData.a2_gradiant_max_row}," +
                   $"{GlobalData.a2_gradiant_max_col}," +
                   $"{GlobalData.a2_gradiant_angle}," +
                   $"{GlobalData.a3_gradiant_average}," +
                   $"{GlobalData.a3_gradiant_max}," +
                   $"{GlobalData.a3_gradiant_max_row}," +
                   $"{GlobalData.a3_gradiant_max_col}," +
                   $"{GlobalData.a3_gradiant_angle}," +
                   $"{GlobalData.a4_gradiant_average}," +
                   $"{GlobalData.a4_gradiant_max}," +
                   $"{GlobalData.a4_gradiant_max_row}," +
                   $"{GlobalData.a4_gradiant_max_col}," +
                   $"{GlobalData.a4_gradiant_angle}," +
                   $"{GlobalData.a1_max}," +
                   $"{GlobalData.a1_min}," +
                   $"{GlobalData.a1_max_row}," +
                   $"{GlobalData.a1_max_col}," +
                   $"{GlobalData.a1_min_row}," +
                   $"{GlobalData.a1_min_col}," +
                   $"{GlobalData.a2_max}," +
                   $"{GlobalData.a2_min}," +
                   $"{GlobalData.a2_max_row}," +
                   $"{GlobalData.a2_max_col}," +
                   $"{GlobalData.a2_min_row}," +
                   $"{GlobalData.a2_min_col}," +
                   $"{GlobalData.a3_max}," +
                   $"{GlobalData.a3_min}," +
                   $"{GlobalData.a3_max_row}," +
                   $"{GlobalData.a3_max_col}," +
                   $"{GlobalData.a3_min_row}," +
                   $"{GlobalData.a3_min_col}," +
                   $"{GlobalData.a4_max}," +
                   $"{GlobalData.a4_min}," +
                   $"{GlobalData.a4_max_row}," +
                   $"{GlobalData.a4_max_col}," +
                   $"{GlobalData.a4_min_row}," +
                   $"{GlobalData.a4_min_col}," +
                   $"{GlobalData.b1_pv}," +
                   $"{GlobalData.b1_pv99}," +
                   $"{GlobalData.b2_pv}," +
                   $"{GlobalData.b2_pv99}," +
                   $"{GlobalData.b3_pv}," +
                   $"{GlobalData.b3_pv99}," +
                   $"{GlobalData.b4_pv}," +
                   $"{GlobalData.b4_pv99}," +
                   $"{GlobalData.b1_gradiant_average}," +
                   $"{GlobalData.b1_gradiant_max}," +
                   $"{GlobalData.b1_gradiant_max_row}," +
                   $"{GlobalData.b1_gradiant_max_col}," +
                   $"{GlobalData.b1_gradiant_angle}," +
                   $"{GlobalData.b2_gradiant_average}," +
                   $"{GlobalData.b2_gradiant_max}," +
                   $"{GlobalData.b2_gradiant_max_row}," +
                   $"{GlobalData.b2_gradiant_max_col}," +
                   $"{GlobalData.b2_gradiant_angle}," +
                   $"{GlobalData.b3_gradiant_average}," +
                   $"{GlobalData.b3_gradiant_max}," +
                   $"{GlobalData.b3_gradiant_max_row}," +
                   $"{GlobalData.b3_gradiant_max_col}," +
                   $"{GlobalData.b3_gradiant_angle}," +
                   $"{GlobalData.b4_gradiant_average}," +
                   $"{GlobalData.b4_gradiant_max}," +
                   $"{GlobalData.b4_gradiant_max_row}," +
                   $"{GlobalData.b4_gradiant_max_col}," +
                   $"{GlobalData.b4_gradiant_angle}," +
                   $"{GlobalData.b1_max}," +
                   $"{GlobalData.b1_min}," +
                   $"{GlobalData.b1_max_row}," +
                   $"{GlobalData.b1_max_col}," +
                   $"{GlobalData.b1_min_row}," +
                   $"{GlobalData.b1_min_col}," +
                   $"{GlobalData.b2_max}," +
                   $"{GlobalData.b2_min}," +
                   $"{GlobalData.b2_max_row}," +
                   $"{GlobalData.b2_max_col}," +
                   $"{GlobalData.b2_min_row}," +
                   $"{GlobalData.b2_min_col}," +
                   $"{GlobalData.b3_max}," +
                   $"{GlobalData.b3_min}," +
                   $"{GlobalData.b3_max_row}," +
                   $"{GlobalData.b3_max_col}," +
                   $"{GlobalData.b3_min_row}," +
                   $"{GlobalData.b3_min_col}," +
                   $"{GlobalData.b4_max}," +
                   $"{GlobalData.b4_min}," +
                   $"{GlobalData.b4_max_row}," +
                   $"{GlobalData.b4_max_col}," +
                   $"{GlobalData.b4_min_row}," +
                   $"{GlobalData.b4_min_col}," +
                   $"{GlobalData.c1_pv}," +
                   $"{GlobalData.c1_pv99}," +
                   $"{GlobalData.c2_pv}," +
                   $"{GlobalData.c2_pv99}," +
                   $"{GlobalData.c3_pv}," +
                   $"{GlobalData.c3_pv99}," +
                   $"{GlobalData.c4_pv}," +
                   $"{GlobalData.c4_pv99}," +
                   $"{GlobalData.c1_gradiant_average}," +
                   $"{GlobalData.c1_gradiant_max}," +
                   $"{GlobalData.c1_gradiant_max_row}," +
                   $"{GlobalData.c1_gradiant_max_col}," +
                   $"{GlobalData.c1_gradiant_angle}," +
                   $"{GlobalData.c2_gradiant_average}," +
                   $"{GlobalData.c2_gradiant_max}," +
                   $"{GlobalData.c2_gradiant_max_row}," +
                   $"{GlobalData.c2_gradiant_max_col}," +
                   $"{GlobalData.c2_gradiant_angle}," +
                   $"{GlobalData.c3_gradiant_average}," +
                   $"{GlobalData.c3_gradiant_max}," +
                   $"{GlobalData.c3_gradiant_max_row}," +
                   $"{GlobalData.c3_gradiant_max_col}," +
                   $"{GlobalData.c3_gradiant_angle}," +
                   $"{GlobalData.c4_gradiant_average}," +
                   $"{GlobalData.c4_gradiant_max}," +
                   $"{GlobalData.c4_gradiant_max_row}," +
                   $"{GlobalData.c4_gradiant_max_col}," +
                   $"{GlobalData.c4_gradiant_angle}," +
                   $"{GlobalData.c1_max}," +
                   $"{GlobalData.c1_min}," +
                   $"{GlobalData.c1_max_row}," +
                   $"{GlobalData.c1_max_col}," +
                   $"{GlobalData.c1_min_row}," +
                   $"{GlobalData.c1_min_col}," +
                   $"{GlobalData.c2_max}," +
                   $"{GlobalData.c2_min}," +
                   $"{GlobalData.c2_max_row}," +
                   $"{GlobalData.c2_max_col}," +
                   $"{GlobalData.c2_min_row}," +
                   $"{GlobalData.c2_min_col}," +
                   $"{GlobalData.c3_max}," +
                   $"{GlobalData.c3_min}," +
                   $"{GlobalData.c3_max_row}," +
                   $"{GlobalData.c3_max_col}," +
                   $"{GlobalData.c3_min_row}," +
                   $"{GlobalData.c3_min_col}," +
                   $"{GlobalData.c4_max}," +
                   $"{GlobalData.c4_min}," +
                   $"{GlobalData.c4_max_row}," +
                   $"{GlobalData.c4_max_col}," +
                   $"{GlobalData.c4_min_row}," +
                   $"{GlobalData.c4_min_col}";
        }

        private void setAInfo()
        {
            label290.Text = GlobalData.a1_pv99.ToString("F2");
            label266.Text = GlobalData.a2_pv99.ToString("F2");
            label241.Text = GlobalData.a3_pv99.ToString("F2");
            label240.Text = GlobalData.a4_pv99.ToString("F2");
            label287.Text = GlobalData.a1_gradiant_average.ToString("F2");
            label289.Text = GlobalData.a1_gradiant_max.ToString("F2");
            label285.Text = GlobalData.a1_gradiant_max_row.ToString();
            label283.Text = GlobalData.a1_gradiant_max_col.ToString();
            label281.Text = GlobalData.a1_gradiant_angle.ToString("F2");

            label278.Text = GlobalData.a1_max.ToString("F2");
            label279.Text = GlobalData.a1_max_row.ToString();
            label276.Text = GlobalData.a1_max_col.ToString();
            label272.Text = GlobalData.a1_min.ToString("F2");
            label273.Text = GlobalData.a1_min_row.ToString();
            label270.Text = GlobalData.a1_min_col.ToString();

            label263.Text = GlobalData.a2_gradiant_average.ToString("F2");
            label265.Text = GlobalData.a2_gradiant_max.ToString("F2");
            label261.Text = GlobalData.a2_gradiant_max_row.ToString();
            label259.Text = GlobalData.a2_gradiant_max_col.ToString();
            label257.Text = GlobalData.a2_gradiant_angle.ToString("F2");

            label254.Text = GlobalData.a2_max.ToString("F2");
            label255.Text = GlobalData.a2_max_row.ToString();
            label252.Text = GlobalData.a2_max_col.ToString();
            label248.Text = GlobalData.a2_min.ToString("F2");
            label249.Text = GlobalData.a2_min_row.ToString();
            label246.Text = GlobalData.a2_min_col.ToString();

            label237.Text = GlobalData.a3_gradiant_average.ToString("F2");
            label239.Text = GlobalData.a3_gradiant_max.ToString("F2");
            label235.Text = GlobalData.a3_gradiant_max_row.ToString();
            label233.Text = GlobalData.a3_gradiant_max_col.ToString();
            label231.Text = GlobalData.a3_gradiant_angle.ToString("F2");

            label218.Text = GlobalData.a3_max.ToString("F2");
            label219.Text = GlobalData.a3_max_row.ToString();
            label216.Text = GlobalData.a3_max_col.ToString();
            label212.Text = GlobalData.a3_min.ToString("F2");
            label213.Text = GlobalData.a3_min_row.ToString();
            label210.Text = GlobalData.a3_min_col.ToString();

            label228.Text = GlobalData.a4_gradiant_average.ToString("F2");
            label230.Text = GlobalData.a4_gradiant_max.ToString("F2");
            label226.Text = GlobalData.a4_gradiant_max_row.ToString();
            label224.Text = GlobalData.a4_gradiant_max_col.ToString();
            label222.Text = GlobalData.a4_gradiant_angle.ToString("F2");

            label206.Text = GlobalData.a4_max.ToString("F2");
            label207.Text = GlobalData.a4_max_row.ToString();
            label204.Text = GlobalData.a4_max_col.ToString();
            label200.Text = GlobalData.a4_min.ToString("F2");
            label201.Text = GlobalData.a4_min_row.ToString();
            label198.Text = GlobalData.a4_min_col.ToString();
        }

        private void setBInfo()
        {
            label194.Text = GlobalData.b1_pv99.ToString("F2");
            label170.Text = GlobalData.b2_pv99.ToString("F2");
            label145.Text = GlobalData.b3_pv99.ToString("F2");
            label144.Text = GlobalData.b4_pv99.ToString("F2");
            label191.Text = GlobalData.b1_gradiant_average.ToString("F2");
            label193.Text = GlobalData.b1_gradiant_max.ToString("F2");
            label189.Text = GlobalData.b1_gradiant_max_row.ToString();
            label187.Text = GlobalData.b1_gradiant_max_col.ToString();
            label185.Text = GlobalData.b1_gradiant_angle.ToString("F2");

            label182.Text = GlobalData.b1_max.ToString("F2");
            label183.Text = GlobalData.b1_max_row.ToString();
            label180.Text = GlobalData.b1_max_col.ToString();
            label176.Text = GlobalData.b1_min.ToString("F2");
            label177.Text = GlobalData.b1_min_row.ToString();
            label174.Text = GlobalData.b1_min_col.ToString();

            label167.Text = GlobalData.b2_gradiant_average.ToString("F2");
            label169.Text = GlobalData.b2_gradiant_max.ToString("F2");
            label165.Text = GlobalData.b2_gradiant_max_row.ToString();
            label163.Text = GlobalData.b2_gradiant_max_col.ToString();
            label161.Text = GlobalData.b2_gradiant_angle.ToString("F2");

            label158.Text = GlobalData.b2_max.ToString("F2");
            label159.Text = GlobalData.b2_max_row.ToString();
            label156.Text = GlobalData.b2_max_col.ToString();
            label152.Text = GlobalData.b2_min.ToString("F2");
            label153.Text = GlobalData.b2_min_row.ToString();
            label150.Text = GlobalData.b2_min_col.ToString();

            label141.Text = GlobalData.b3_gradiant_average.ToString("F2");
            label143.Text = GlobalData.b3_gradiant_max.ToString("F2");
            label139.Text = GlobalData.b3_gradiant_max_row.ToString();
            label137.Text = GlobalData.b3_gradiant_max_col.ToString();
            label135.Text = GlobalData.b3_gradiant_angle.ToString("F2");

            label122.Text = GlobalData.b3_max.ToString("F2");
            label123.Text = GlobalData.b3_max_row.ToString();
            label120.Text = GlobalData.b3_max_col.ToString();
            label116.Text = GlobalData.b3_min.ToString("F2");
            label117.Text = GlobalData.b3_min_row.ToString();
            label114.Text = GlobalData.b3_min_col.ToString();

            label132.Text = GlobalData.b4_gradiant_average.ToString("F2");
            label134.Text = GlobalData.b4_gradiant_max.ToString("F2");
            label130.Text = GlobalData.b4_gradiant_max_row.ToString();
            label128.Text = GlobalData.b4_gradiant_max_col.ToString();
            label126.Text = GlobalData.b4_gradiant_angle.ToString("F2");

            label110.Text = GlobalData.b4_max.ToString("F2");
            label111.Text = GlobalData.b4_max_row.ToString();
            label108.Text = GlobalData.b4_max_col.ToString();
            label104.Text = GlobalData.b4_min.ToString("F2");
            label105.Text = GlobalData.b4_min_row.ToString();
            label102.Text = GlobalData.b4_min_col.ToString();
        }

        private void setCInfo()
        {
            // 显示c1-c4的PV99值
            label5.Text = GlobalData.c1_pv99.ToString("F2");
            label50.Text = GlobalData.c2_pv99.ToString("F2");
            label74.Text = GlobalData.c3_pv99.ToString("F2");
            label98.Text = GlobalData.c4_pv99.ToString("F2");

            // 显示c1的梯度数据
            label7.Text = GlobalData.c1_gradiant_average.ToString("F2");
            label9.Text = GlobalData.c1_gradiant_max.ToString("F2");
            label19.Text = GlobalData.c1_gradiant_max_row.ToString();
            label21.Text = GlobalData.c1_gradiant_max_col.ToString();
            label23.Text = GlobalData.c1_gradiant_angle.ToString("F2");

            // 显示c2的梯度数据
            label47.Text = GlobalData.c2_gradiant_average.ToString("F2");
            label49.Text = GlobalData.c2_gradiant_max.ToString("F2");
            label45.Text = GlobalData.c2_gradiant_max_row.ToString();
            label43.Text = GlobalData.c2_gradiant_max_col.ToString();
            label41.Text = GlobalData.c2_gradiant_angle.ToString("F2");

            // 显示c3的梯度数据
            label71.Text = GlobalData.c3_gradiant_average.ToString("F2");
            label73.Text = GlobalData.c3_gradiant_max.ToString("F2");
            label69.Text = GlobalData.c3_gradiant_max_row.ToString();
            label67.Text = GlobalData.c3_gradiant_max_col.ToString();
            label65.Text = GlobalData.c3_gradiant_angle.ToString("F2");

            // 显示c4的梯度数据
            label95.Text = GlobalData.c4_gradiant_average.ToString("F2");
            label97.Text = GlobalData.c4_gradiant_max.ToString("F2");
            label93.Text = GlobalData.c4_gradiant_max_row.ToString();
            label91.Text = GlobalData.c4_gradiant_max_col.ToString();
            label89.Text = GlobalData.c4_gradiant_angle.ToString("F2");

            // 显示c1的极值数据
            label30.Text = GlobalData.c1_max.ToString("F2");
            label32.Text = GlobalData.c1_max_row.ToString();
            label28.Text = GlobalData.c1_max_col.ToString();
            label34.Text = GlobalData.c1_min.ToString("F2");
            label35.Text = GlobalData.c1_min_row.ToString();
            label26.Text = GlobalData.c1_min_col.ToString();

            // 显示c2的极值数据
            label38.Text = GlobalData.c2_max.ToString("F2");
            label39.Text = GlobalData.c2_max_row.ToString();
            label36.Text = GlobalData.c2_max_col.ToString();
            label14.Text = GlobalData.c2_min.ToString("F2");
            label15.Text = GlobalData.c2_min_row.ToString();
            label12.Text = GlobalData.c2_min_col.ToString();

            // 显示c3的极值数据
            label62.Text = GlobalData.c3_max.ToString("F2");
            label63.Text = GlobalData.c3_max_row.ToString();
            label60.Text = GlobalData.c3_max_col.ToString();
            label56.Text = GlobalData.c3_min.ToString("F2");
            label57.Text = GlobalData.c3_min_row.ToString();
            label54.Text = GlobalData.c3_min_col.ToString();

            // 显示c4的极值数据
            label86.Text = GlobalData.c4_max.ToString("F2");
            label87.Text = GlobalData.c4_max_row.ToString();
            label84.Text = GlobalData.c4_max_col.ToString();
            label80.Text = GlobalData.c4_min.ToString("F2");
            label81.Text = GlobalData.c4_min_row.ToString();
            label78.Text = GlobalData.c4_min_col.ToString();
        }

        private void comboBox1_SelectedValueChanged(object sender, EventArgs e)
        {
            if (comboBox1.SelectedItem != null)
            {
                //d_single_update();
                //if (comboBox2.SelectedItem != null)
                    //match_update();
            }
        }

        private void comboBox2_SelectedValueChanged(object sender, EventArgs e)
        {
            if (comboBox2.SelectedItem != null)
            {
                //x_single_update();
                //if (comboBox1.SelectedItem != null)
                    //match_update();
            }
        }

        /// <summary>
        /// 保存单个棱镜的CSV数据，避免数据被后续计算覆盖
        /// </summary>
        /// <param name="bigSn">大棱镜SN</param>
        /// <param name="smallSn">小棱镜SN</param>
        /// <param name="savePath">保存路径</param>
        private static void SaveIndividualCsvData(string bigSn, string smallSn, string savePath)
        {
            string csvFile = Path.Combine(savePath, "data.csv");
            bool fileExists = File.Exists(csvFile);

            try
            {
                using (var writer = new StreamWriter(csvFile, append: true))
                {
                    if (!fileExists)
                    {
                        writer.WriteLine(GetCsvHeaders());
                    }

                    // 创建当前数据的快照，避免被后续计算覆盖
                    string csvData = GetCurrentCsvValues(bigSn, smallSn);
                    writer.WriteLine(csvData);
                }
            }
            catch (IOException ex)
            {
                MessageBox.Show(
                    $"写入单个棱镜CSV文件时发生错误: {ex.Message}",
                    "文件操作错误",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 获取当前GlobalData中的CSV值，创建数据快照
        /// </summary>
        /// <param name="bigSn">大棱镜SN</param>
        /// <param name="smallSn">小棱镜SN</param>
        /// <returns>CSV格式的数据行</returns>
        private static string GetCurrentCsvValues(string bigSn, string smallSn)
        {
            return $"{bigSn ?? ""}," +
                   $"{smallSn ?? ""}," +
                   $"{GlobalData.pv99_val}," +
                   $"{GlobalData.gradiant_average}," +
                   $"{GlobalData.gradiant_max}," +
                   $"{GlobalData.gradiant_max_row}," +
                   $"{GlobalData.gradiant_max_col}," +
                   $"{GlobalData.gradiant_angle}," +
                   $"{GlobalData.max}," +
                   $"{GlobalData.min}," +
                   $"{GlobalData.max_row}," +
                   $"{GlobalData.max_col}," +
                   $"{GlobalData.min_row}," +
                   $"{GlobalData.min_col}," +
                   $"{GlobalData.a1_pv}," +
                   $"{GlobalData.a1_pv99}," +
                   $"{GlobalData.a2_pv}," +
                   $"{GlobalData.a2_pv99}," +
                   $"{GlobalData.a3_pv}," +
                   $"{GlobalData.a3_pv99}," +
                   $"{GlobalData.a4_pv}," +
                   $"{GlobalData.a4_pv99}," +
                   $"{GlobalData.a1_gradiant_average}," +
                   $"{GlobalData.a1_gradiant_max}," +
                   $"{GlobalData.a1_gradiant_max_row}," +
                   $"{GlobalData.a1_gradiant_max_col}," +
                   $"{GlobalData.a1_gradiant_angle}," +
                   $"{GlobalData.a2_gradiant_average}," +
                   $"{GlobalData.a2_gradiant_max}," +
                   $"{GlobalData.a2_gradiant_max_row}," +
                   $"{GlobalData.a2_gradiant_max_col}," +
                   $"{GlobalData.a2_gradiant_angle}," +
                   $"{GlobalData.a3_gradiant_average}," +
                   $"{GlobalData.a3_gradiant_max}," +
                   $"{GlobalData.a3_gradiant_max_row}," +
                   $"{GlobalData.a3_gradiant_max_col}," +
                   $"{GlobalData.a3_gradiant_angle}," +
                   $"{GlobalData.a4_gradiant_average}," +
                   $"{GlobalData.a4_gradiant_max}," +
                   $"{GlobalData.a4_gradiant_max_row}," +
                   $"{GlobalData.a4_gradiant_max_col}," +
                   $"{GlobalData.a4_gradiant_angle}," +
                   $"{GlobalData.a1_max}," +
                   $"{GlobalData.a1_min}," +
                   $"{GlobalData.a1_max_row}," +
                   $"{GlobalData.a1_max_col}," +
                   $"{GlobalData.a1_min_row}," +
                   $"{GlobalData.a1_min_col}," +
                   $"{GlobalData.a2_max}," +
                   $"{GlobalData.a2_min}," +
                   $"{GlobalData.a2_max_row}," +
                   $"{GlobalData.a2_max_col}," +
                   $"{GlobalData.a2_min_row}," +
                   $"{GlobalData.a2_min_col}," +
                   $"{GlobalData.a3_max}," +
                   $"{GlobalData.a3_min}," +
                   $"{GlobalData.a3_max_row}," +
                   $"{GlobalData.a3_max_col}," +
                   $"{GlobalData.a3_min_row}," +
                   $"{GlobalData.a3_min_col}," +
                   $"{GlobalData.a4_max}," +
                   $"{GlobalData.a4_min}," +
                   $"{GlobalData.a4_max_row}," +
                   $"{GlobalData.a4_max_col}," +
                   $"{GlobalData.a4_min_row}," +
                   $"{GlobalData.a4_min_col}," +
                   $"{GlobalData.b1_pv}," +
                   $"{GlobalData.b1_pv99}," +
                   $"{GlobalData.b2_pv}," +
                   $"{GlobalData.b2_pv99}," +
                   $"{GlobalData.b3_pv}," +
                   $"{GlobalData.b3_pv99}," +
                   $"{GlobalData.b4_pv}," +
                   $"{GlobalData.b4_pv99}," +
                   $"{GlobalData.b1_gradiant_average}," +
                   $"{GlobalData.b1_gradiant_max}," +
                   $"{GlobalData.b1_gradiant_max_row}," +
                   $"{GlobalData.b1_gradiant_max_col}," +
                   $"{GlobalData.b1_gradiant_angle}," +
                   $"{GlobalData.b2_gradiant_average}," +
                   $"{GlobalData.b2_gradiant_max}," +
                   $"{GlobalData.b2_gradiant_max_row}," +
                   $"{GlobalData.b2_gradiant_max_col}," +
                   $"{GlobalData.b2_gradiant_angle}," +
                   $"{GlobalData.b3_gradiant_average}," +
                   $"{GlobalData.b3_gradiant_max}," +
                   $"{GlobalData.b3_gradiant_max_row}," +
                   $"{GlobalData.b3_gradiant_max_col}," +
                   $"{GlobalData.b3_gradiant_angle}," +
                   $"{GlobalData.b4_gradiant_average}," +
                   $"{GlobalData.b4_gradiant_max}," +
                   $"{GlobalData.b4_gradiant_max_row}," +
                   $"{GlobalData.b4_gradiant_max_col}," +
                   $"{GlobalData.b4_gradiant_angle}," +
                   $"{GlobalData.b1_max}," +
                   $"{GlobalData.b1_min}," +
                   $"{GlobalData.b1_max_row}," +
                   $"{GlobalData.b1_max_col}," +
                   $"{GlobalData.b1_min_row}," +
                   $"{GlobalData.b1_min_col}," +
                   $"{GlobalData.b2_max}," +
                   $"{GlobalData.b2_min}," +
                   $"{GlobalData.b2_max_row}," +
                   $"{GlobalData.b2_max_col}," +
                   $"{GlobalData.b2_min_row}," +
                   $"{GlobalData.b2_min_col}," +
                   $"{GlobalData.b3_max}," +
                   $"{GlobalData.b3_min}," +
                   $"{GlobalData.b3_max_row}," +
                   $"{GlobalData.b3_max_col}," +
                   $"{GlobalData.b3_min_row}," +
                   $"{GlobalData.b3_min_col}," +
                   $"{GlobalData.b4_max}," +
                   $"{GlobalData.b4_min}," +
                   $"{GlobalData.b4_max_row}," +
                   $"{GlobalData.b4_max_col}," +
                   $"{GlobalData.b4_min_row}," +
                   $"{GlobalData.b4_min_col}," +
                   $"{GlobalData.c1_pv}," +
                   $"{GlobalData.c1_pv99}," +
                   $"{GlobalData.c2_pv}," +
                   $"{GlobalData.c2_pv99}," +
                   $"{GlobalData.c3_pv}," +
                   $"{GlobalData.c3_pv99}," +
                   $"{GlobalData.c4_pv}," +
                   $"{GlobalData.c4_pv99}," +
                   $"{GlobalData.c1_gradiant_average}," +
                   $"{GlobalData.c1_gradiant_max}," +
                   $"{GlobalData.c1_gradiant_max_row}," +
                   $"{GlobalData.c1_gradiant_max_col}," +
                   $"{GlobalData.c1_gradiant_angle}," +
                   $"{GlobalData.c2_gradiant_average}," +
                   $"{GlobalData.c2_gradiant_max}," +
                   $"{GlobalData.c2_gradiant_max_row}," +
                   $"{GlobalData.c2_gradiant_max_col}," +
                   $"{GlobalData.c2_gradiant_angle}," +
                   $"{GlobalData.c3_gradiant_average}," +
                   $"{GlobalData.c3_gradiant_max}," +
                   $"{GlobalData.c3_gradiant_max_row}," +
                   $"{GlobalData.c3_gradiant_max_col}," +
                   $"{GlobalData.c3_gradiant_angle}," +
                   $"{GlobalData.c4_gradiant_average}," +
                   $"{GlobalData.c4_gradiant_max}," +
                   $"{GlobalData.c4_gradiant_max_row}," +
                   $"{GlobalData.c4_gradiant_max_col}," +
                   $"{GlobalData.c4_gradiant_angle}," +
                   $"{GlobalData.c1_max}," +
                   $"{GlobalData.c1_min}," +
                   $"{GlobalData.c1_max_row}," +
                   $"{GlobalData.c1_max_col}," +
                   $"{GlobalData.c1_min_row}," +
                   $"{GlobalData.c1_min_col}," +
                   $"{GlobalData.c2_max}," +
                   $"{GlobalData.c2_min}," +
                   $"{GlobalData.c2_max_row}," +
                   $"{GlobalData.c2_max_col}," +
                   $"{GlobalData.c2_min_row}," +
                   $"{GlobalData.c2_min_col}," +
                   $"{GlobalData.c3_max}," +
                   $"{GlobalData.c3_min}," +
                   $"{GlobalData.c3_max_row}," +
                   $"{GlobalData.c3_max_col}," +
                   $"{GlobalData.c3_min_row}," +
                   $"{GlobalData.c3_min_col}," +
                   $"{GlobalData.c4_max}," +
                   $"{GlobalData.c4_min}," +
                   $"{GlobalData.c4_max_row}," +
                   $"{GlobalData.c4_max_col}," +
                   $"{GlobalData.c4_min_row}," +
                   $"{GlobalData.c4_min_col}";
        }

        /// <summary>
        /// 更新界面显示最后一对计算结果
        /// </summary>
        /// <param name="bigSn">大棱镜SN，如果为null则只显示小棱镜数据</param>
        /// <param name="smallSn">小棱镜SN，如果为null则只显示大棱镜数据</param>
        private void UpdateDisplayForLastResult(string bigSn, string smallSn)
        {
            try
            {
                // 更新SN显示
                if (!string.IsNullOrEmpty(bigSn))
                {
                    GlobalData.big_prism_sn = bigSn;
                }
                if (!string.IsNullOrEmpty(smallSn))
                {
                    GlobalData.small_prism_sn = smallSn;
                }

                // 根据情况更新界面显示
                if (!string.IsNullOrEmpty(bigSn) && !string.IsNullOrEmpty(smallSn))
                {
                    // 配对模式：显示两个棱镜的数据
                    setAInfo(); // 显示大棱镜数据
                    setBInfo(); // 显示小棱镜数据
                    setCInfo(); // 显示c1-c4数据

                    // 更新状态标签显示当前显示的是哪一对
                    D_status_label.Text = $"大: {bigSn}";
                    X_status_label.Text = $"小: {smallSn}";
                }
                else if (!string.IsNullOrEmpty(bigSn))
                {
                    // 只有大棱镜
                    setAInfo(); // 显示大棱镜数据
                    setCInfo(); // 显示c1-c4数据
                    D_status_label.Text = $"大: {bigSn}";
                    X_status_label.Text = "无";
                }
                else if (!string.IsNullOrEmpty(smallSn))
                {
                    // 只有小棱镜
                    setBInfo(); // 显示小棱镜数据
                    setCInfo(); // 显示c1-c4数据
                    D_status_label.Text = "无";
                    X_status_label.Text = $"小: {smallSn}";
                }
            }
            catch (Exception ex)
            {
                Logs.WriteError($"更新界面显示失败: {ex.Message}", true);
                errMsgShow("界面更新失败");
            }
        }

        // Windows API声明
        [DllImport("imm32.dll")]
        private static extern IntPtr ImmGetContext(IntPtr hWnd);

        [DllImport("imm32.dll")]
        private static extern bool ImmSetOpenStatus(IntPtr hIMC, bool bOpen);

        [DllImport("imm32.dll")]
        private static extern bool ImmSetConversionStatus(IntPtr hIMC, int fdwConversion, int fdwSentence);

        [DllImport("imm32.dll")]
        private static extern bool ImmReleaseContext(IntPtr hWnd, IntPtr hIMC);
        private void backgroundWorker1_DoWork(object sender, DoWorkEventArgs e)
        {
            var worker = sender as BackgroundWorker;
            var arguments = e.Argument as Dictionary<string, List<string>>;
            var bigPrismSNs = arguments["bigPrismSNs"];
            var smallPrismSNs = arguments["smallPrismSNs"];
            int bigCount = bigPrismSNs.Count;
            int smallCount = smallPrismSNs.Count;
            int totalOperations = bigCount > 0 && smallCount > 0 ? bigCount * 3 : bigCount + smallCount;
            int completedOperations = 0;

            var results = new Dictionary<string, object>();
            string lastBigSn = "";
            string lastSmallSn = "";
            string completionMessage = "";
            bool hasError = false;

            try
            {
                // Mode 1: Single Big Prism
                if (bigCount > 0 && smallCount == 0)
                {
                    foreach (var sn in bigPrismSNs)
                    {
                        if (worker.CancellationPending) { e.Cancel = true; return; }
                        if (ProcessSinglePrism(sn, "big"))
                        {
                            lastBigSn = sn;
                        }
                        else
                        {
                            hasError = true;
                            Logs.WriteError($"大棱镜 {sn} 计算失败", true);
                        }
                        completedOperations++;
                        worker.ReportProgress((int)((double)completedOperations / totalOperations * 100));
                    }
                    completionMessage = "大棱镜计算完成。";
                }
                // Mode 2: Single Small Prism
                else if (smallCount > 0 && bigCount == 0)
                {
                    foreach (var sn in smallPrismSNs)
                    {
                        if (worker.CancellationPending) { e.Cancel = true; return; }
                        if (ProcessSinglePrism(sn, "small"))
                        {
                            lastSmallSn = sn;
                        }
                        else
                        {
                            hasError = true;
                            Logs.WriteError($"小棱镜 {sn} 计算失败", true);
                        }
                        completedOperations++;
                        worker.ReportProgress((int)((double)completedOperations / totalOperations * 100));
                    }
                    completionMessage = "小棱镜计算完成。";
                }
                // Mode 3: Dual Prism Pairing
                else if (bigCount > 0 && smallCount > 0)
                {
                    for (int i = 0; i < bigCount; i++)
                    {
                        if (worker.CancellationPending) { e.Cancel = true; return; }
                        string bigSn = bigPrismSNs[i];
                        string smallSn = smallPrismSNs[i];
                        bool pairSuccess = true;

                        string bigSnSaveDir = $"data_result/{bigSn}/";
                        string smallSnSaveDir = $"data_result/{smallSn}/";
                        string pairSaveDir = $"data_result/{bigSn}_{smallSn}/";
                        Directory.CreateDirectory(bigSnSaveDir);
                        Directory.CreateDirectory(smallSnSaveDir);
                        Directory.CreateDirectory(pairSaveDir);

                        // 1. Big Prism
                        if (pv_handle(bigSn, 0) != 0) { pairSuccess = false; Logs.WriteError($"配对计算失败，无法处理大棱镜SN: {bigSn}", true); }
                        else
                        {
                            List<DataEntry> entriesBig = CalcPV.ReadDataFromFile(GlobalData.big_prism_fname);
                            if (CalcPV.ProcessSingleBigPrism(entriesBig, $"{bigSnSaveDir}{bigSn}.png", $"{bigSnSaveDir}{bigSn}_梯度.png") != 0) { pairSuccess = false; Logs.WriteError($"配对计算失败，大棱镜计算部分失败: {bigSn}", true); }
                            else { SaveIndividualCsvData(bigSn, null, bigSnSaveDir); }
                        }
                        completedOperations++;
                        worker.ReportProgress((int)((double)completedOperations / totalOperations * 100));
                        if (!pairSuccess) { hasError = true; continue; }

                        // 2. Small Prism
                        if (pv_handle(smallSn, 1) != 0) { pairSuccess = false; Logs.WriteError($"配对计算失败，无法处理小棱镜SN: {smallSn}", true); }
                        else
                        {
                            List<DataEntry> entriesSmall = CalcPV.ReadDataFromFile(GlobalData.small_prism_fname);
                            if (CalcPV.ProcessSingleSmallPrism(entriesSmall, $"{smallSnSaveDir}{smallSn}.png", $"{smallSnSaveDir}{smallSn}_梯度.png") != 0) { pairSuccess = false; Logs.WriteError($"配对计算失败，小棱镜计算部分失败: {smallSn}", true); }
                            else { SaveIndividualCsvData(null, smallSn, smallSnSaveDir); }
                        }
                        completedOperations++;
                        worker.ReportProgress((int)((double)completedOperations / totalOperations * 100));
                        if (!pairSuccess) { hasError = true; continue; }

                        // 3. Paired
                        List<DataEntry> finalEntriesBig = CalcPV.ReadDataFromFile(GlobalData.big_prism_fname);
                        List<DataEntry> finalEntriesSmall = CalcPV.ReadDataFromFile(GlobalData.small_prism_fname);
                        string pairedSurfaceMap = $"{pairSaveDir}{bigSn}_{smallSn}.png";
                        string pairedGradientMap = $"{pairSaveDir}{bigSn}_{smallSn}_梯度.png";
                        if (CalcPV.ProcessMatrices(finalEntriesBig, finalEntriesSmall, pairedSurfaceMap, pairedGradientMap) != 0) { pairSuccess = false; Logs.WriteError($"配对计算失败: {bigSn} & {smallSn}", true); }
                        else
                        {
                            AppendOrCreateCsv(bigSn, smallSn, pairSaveDir);

                            // Generate advanced annotated image for the pair
                            try
                            {
                                // Combine points for the image
                                var allPoints = new List<DataEntry>(finalEntriesBig);
                                allPoints.AddRange(finalEntriesSmall);

                                var imageData = new ImageGenerationData
                                {
                                    AllPoints = allPoints,
                                    Metrics = GlobalData.GetMetricsSnapshot(),
                                    OutputPath = Path.Combine(pairSaveDir, $"{bigSn}_{smallSn}_annotated.png")
                                };
                                AdvancedImageGenerator.Generate(imageData);
                            }
                            catch (Exception ex)
                            {
                                Logs.WriteError($"为配对 {bigSn}_{smallSn} 生成高级图像失败: {ex.Message}", true);
                            }
                        }
                        completedOperations++;
                        worker.ReportProgress((int)((double)completedOperations / totalOperations * 100));
                        if (!pairSuccess) { hasError = true; continue; }

                        if (pairSuccess)
                        {
                            lastBigSn = bigSn;
                            lastSmallSn = smallSn;
                        }
                    }
                    completionMessage = "双棱镜配对计算完成。";
                }
            }
            catch (Exception ex)
            {
                Logs.WriteError($"后台计算时发生未预料的错误: {ex.Message}", true);
                hasError = true;
                completionMessage = "计算过程中发生严重错误。";
            }

            results["lastBigSn"] = lastBigSn;
            results["lastSmallSn"] = lastSmallSn;
            results["hasError"] = hasError;
            results["completionMessage"] = completionMessage;
            e.Result = results;
        }

        private void backgroundWorker1_ProgressChanged(object sender, ProgressChangedEventArgs e)
        {
            progressBar1.Value = e.ProgressPercentage;
        }

        private void backgroundWorker1_RunWorkerCompleted(object sender, RunWorkerCompletedEventArgs e)
        {
            // 重新启用按钮并隐藏进度条
            btnCalculate.Enabled = true;
            progressBar1.Visible = false;

            if (e.Cancelled)
            {
                MessageBox.Show("计算已取消。", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
            else if (e.Error != null)
            {
                MessageBox.Show($"计算过程中发生错误: {e.Error.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                Logs.WriteError($"BackgroundWorker 错误: {e.Error.ToString()}", true);
            }
            else
            {
                var results = e.Result as Dictionary<string, object>;
                string lastBigSn = results["lastBigSn"] as string;
                string lastSmallSn = results["lastSmallSn"] as string;
                bool hasError = (bool)results["hasError"];
                string completionMessage = results["completionMessage"] as string;

                // 更新界面显示最后一个成功的结果
                if (!string.IsNullOrEmpty(lastBigSn) || !string.IsNullOrEmpty(lastSmallSn))
                {
                    UpdateDisplayForLastResult(lastBigSn, lastSmallSn);
                }

                if (hasError)
                {
                    errMsgShow("部分计算失败，请检查日志。");
                }
                
                MessageBox.Show(completionMessage, "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }
    }
}
