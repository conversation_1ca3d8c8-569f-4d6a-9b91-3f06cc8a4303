﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Diagnostics;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Nreal_ProductLine_Tool.onlyScan
{
    public partial class PV : FormDefault
    {
        Entrance entrance;
        int sequence, type;
        string title;

        public PV(Entrance entrance, int sequence, string title)
        {
            this.sequence = sequence;
            this.entrance = entrance;
            this.title = title;
            InitializeComponent();
            this.TopLevel = false;
            this.FormBorderStyle = FormBorderStyle.None;
            this.Dock = DockStyle.Fill;
        }

        public override void Start()
        {
            Show();
            pv_val_label.Text = "";
            pv99_val_label.Text = "";
            pv_val_label.BackColor = Color.Gray;
            pv99_val_label.BackColor = Color.Gray;

            int ret = prism_bond.Program.ConvertFormat("Data/"+"1.xyz", "Data/" + "1.txt");
            if(ret == 0)
            prism_bond.Program.ConvertFormat("Data/" + "2.xyz", "Data/" + "2.txt");
            Stopwatch stopwatch = new Stopwatch();
            stopwatch.Start();
            Program1.pv_calculate("Data/" + "1.txt", "Data/" + "2.txt", "Data/" + "1_2.png");
            stopwatch.Stop();
            Console.WriteLine($"ProcessMatrices 方法运行时间: {stopwatch.ElapsedMilliseconds} 毫秒");

            pv_val_label.Text = "" + GlobalData.pv_val;
            pv99_val_label.Text = "" + GlobalData.pv99_val;
        }

        public override void ExcepStop()
        {
            pv_val_label.Text = "";
            pv99_val_label.Text = "";
            pv_val_label.BackColor = Color.Gray;
            pv99_val_label.BackColor = Color.Gray;
        }
    }    
}


