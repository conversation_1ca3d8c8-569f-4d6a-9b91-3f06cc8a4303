using System;
using System.Collections.Generic;
using System.Linq;
using System.IO;
using System.Drawing;
using System.Windows.Forms;

namespace Nreal_ProductLine_Tool.onlyScan
{

    class CalcPV
    {
       
        public static List<DataEntry> ReadDataFromFile(string filePath)
        {
            List<DataEntry> entries = new List<DataEntry>();
            try
            {
                string[] lines = File.ReadAllLines(filePath);
                foreach (string line in lines)
                {
                    string[] parts = line.Split(new[] { ' ' }, StringSplitOptions.RemoveEmptyEntries);
                    if (parts.Length != 3) continue;
                    entries.Add(new DataEntry
                    {
                        Col = int.Parse(parts[0]),
                        Row = int.Parse(parts[1]),
                        Value = double.Parse(parts[2])
                    });
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"读取文件时出错: {ex.Message}");
            }
            return entries;
        }

        private static List<DataEntry> ProcessInput(string input)
        {
            List<DataEntry> entries = new List<DataEntry>();
            string[] lines = input.Split(new[] { '\n', '\r' }, StringSplitOptions.RemoveEmptyEntries);
            foreach (string line in lines)
            {
                string[] parts = line.Split(new[] { ' ' }, StringSplitOptions.RemoveEmptyEntries);
                if (parts.Length != 3) continue;
                entries.Add(new DataEntry
                {
                    Col = int.Parse(parts[0]),
                    Row = int.Parse(parts[1]),
                    Value = double.Parse(parts[2])
                });
            }
            return entries;
        }

        public static int ProcessMatrices(List<DataEntry> entries1, List<DataEntry> entries2, string outFile, string gradientOutFile = null)
        {
            var matrix1Rows = entries1.GroupBy(e => e.Row)
                .Select(g => new RowData
                {
                    RowNumber = g.Key,
                    Columns = g.OrderBy(e => e.Col).ToList()
                })
                .OrderBy(g => g.RowNumber)
                .ToList();

            var matrix2Rows = entries2.GroupBy(e => e.Row)
                .Select(g => new RowData
                {
                    RowNumber = g.Key,
                    Columns = g.OrderBy(e => e.Col).ToList()
                })
                .OrderBy(g => g.RowNumber)
                .ToList();

            if (matrix1Rows.Count == 0 || matrix2Rows.Count == 0)
            {
                Console.WriteLine("输入数据不完整");
                return -1;
            }

            // 找出元素个数最多的行
            var maxElementsRow1 = matrix1Rows.OrderByDescending(row => row.Columns.Count).First();
            var maxElementsRow2 = matrix2Rows.OrderByDescending(row => row.Columns.Count).First();

            int midIndex1 = maxElementsRow1.Columns.Count / 2;
            int midIndex2 = maxElementsRow2.Columns.Count / 2;

            int midCol1 = maxElementsRow1.Columns[midIndex1].Col; // 元素个数最多行的对称轴的原始列坐标
            int midCol2 = maxElementsRow2.Columns[midIndex2].Col; // 元素个数最多行的对称轴的原始列坐标

            int c_1 = 0;
            int c_2 = 0;
            List<DataEntry> results = new List<DataEntry>();
            for (int i = 0; i < Math.Min(matrix1Rows.Count, matrix2Rows.Count); i++)
            {
                var m1Row = matrix1Rows[i];
                var m2Row = matrix2Rows[i];

                // 找到当前行中与第一行对称轴列坐标相同的元素的 index
                int currentMidIndex1 = m1Row.Columns.FindIndex(e => e.Col == midCol1);
                int currentMidIndex2 = m2Row.Columns.FindIndex(e => e.Col == midCol2);

                if (currentMidIndex1 == -1 || currentMidIndex2 == -1)
                {
                    Console.WriteLine($"第 {i + 1} 行无法找到对称轴列坐标");
                    continue;
                }
                if (m1Row.Columns.Count > c_1)
                    c_1 = m1Row.Columns.Count;
                if (m2Row.Columns.Count > c_2)
                    c_2 = m2Row.Columns.Count;
                if(i==0)
                    Console.WriteLine($"c_1:{c_1} c_2:{c_2}");
                int minHalfCols = Math.Min(currentMidIndex1 + 1, currentMidIndex2 + 1);
                minHalfCols = Math.Min(m1Row.Columns.Count / 2 + 1, minHalfCols);
                minHalfCols = Math.Min(m2Row.Columns.Count / 2 + 1, minHalfCols);
                // 处理对称轴及对称轴右侧元素
                for (int offset = 0; offset < minHalfCols; offset++)
                {
                    int index1 = currentMidIndex1 + offset;
                    int index2 = currentMidIndex2 - offset;

                    if (index1 < m1Row.Columns.Count && index2 >= 0)
                    {
                        DataEntry m1Entry = m1Row.Columns[index1];
                        DataEntry m2Entry = m2Row.Columns[index2];

                        double sum = m1Entry.Value + m2Entry.Value;
                        results.Add(new DataEntry
                        {
                            Col = m1Entry.Col,
                            Row = m1Entry.Row,
                            Value = sum
                        });
                    }
                }

                // 处理对称轴左侧元素
                for (int offset = 1; offset < minHalfCols; offset++)
                {
                    int index1 = currentMidIndex1 - offset;
                    int index2 = currentMidIndex2 + offset;

                    if (index1 >= 0 && index2 < m2Row.Columns.Count)
                    {
                        DataEntry m1Entry = m1Row.Columns[index1];
                        DataEntry m2Entry = m2Row.Columns[index2];

                        double sum = m1Entry.Value + m2Entry.Value;
                        results.Add(new DataEntry
                        {
                            Col = m1Entry.Col,
                            Row = m1Entry.Row,
                            Value = sum
                        });
                    }
                }
            }
            //Console.WriteLine($"{c_1} {c_2}");
            // 压合面计算
            //SplitCollection(ref results);
            SplitCollection_c(ref results);
            SplitCollection_a(ref results);
            SplitCollection_b(ref results);
            Logs.WriteInfo("开始压合面型指标计算", true);
            CalculatePV99(ref results, ref GlobalData.pv_val, ref GlobalData.pv99_val);
            GlobalData.ffflag = 1;
            CalculateGradients(ref results, ref GlobalData.gradiant_average, ref GlobalData.gradiant_max, ref GlobalData.gradiant_max_row, ref GlobalData.gradiant_max_col, ref GlobalData.gradiant_angle, gradientOutFile);
            GlobalData.ffflag = 0;
            minVal(ref results, ref GlobalData.max, ref GlobalData.max_row, ref GlobalData.max_col, ref GlobalData.min, ref GlobalData.min_row, ref GlobalData.min_col);

            //DrawData(results, "output.png");
            HeatmapGenerator.Generate(results, outFile);
            //SaveDataToCsv(results, "Data/1.csv");
            Logs.WriteInfo("面型指标计算结束", true);
            return 0;
        }

        static void SplitCollection(ref List<DataEntry> results)
        {
            // 1. 计算行和列的实际范围
            int minRow = results.Min(e => e.Row);
            int maxRow = results.Max(e => e.Row);
            int minCol = results.Min(e => e.Col);
            int maxCol = results.Max(e => e.Col);

            // 2. 计算中心点（向下取整）
            int midRow = (minRow + maxRow) / 2;
            int midCol = (minCol + maxCol) / 2;

            // 2. 初始化四个象限的容器
            var quadrant1 = new List<DataEntry>(); // 右上 (Q1)
            var quadrant2 = new List<DataEntry>(); // 左上 (Q2)
            var quadrant3 = new List<DataEntry>(); // 左下 (Q3)
            var quadrant4 = new List<DataEntry>(); // 右下 (Q4)

            // 3. 遍历并分类数据
            foreach (var entry in results)
            {
                if (entry.Row > midRow)
                {
                    if (entry.Col > midCol)
                        quadrant1.Add(entry);
                    else
                        quadrant2.Add(entry);
                }
                else
                {
                    if (entry.Col <= midCol)
                        quadrant3.Add(entry);
                    else
                        quadrant4.Add(entry);
                }
            }

            // 4. 打印各象限数据量（验证用）
            Console.WriteLine($"分割结果：");
            Console.WriteLine($"Q1（右上）: {quadrant1.Count} 个点");
            Console.WriteLine($"Q2（左上）: {quadrant2.Count} 个点");
            Console.WriteLine($"Q3（左下）: {quadrant3.Count} 个点");
            Console.WriteLine($"Q4（右下）: {quadrant4.Count} 个点");
        }

        public static void SplitCollection_a(ref List<DataEntry> results)
        {
            int minRow = results.Min(e => e.Row);
            int maxRow = results.Max(e => e.Row);
            int minCol = results.Min(e => e.Col);
            int maxCol = results.Max(e => e.Col);

            int midRow_1_4 = minRow + (maxRow - minRow) / 4;
            int midRow_3_4 = minRow + (maxRow - minRow)*3 / 4;
            int midCol = (minCol + maxCol) / 2;
            int midRow = (minRow + maxRow) / 2;
            int Col_1_3 = minCol + (maxCol - minCol) / 3;
            int Col_2_3 = minCol + (maxCol - minCol) * 2 / 3;

            GlobalData.RegionABounds = new RectangleF(Col_1_3, midRow_1_4, Col_2_3 - Col_1_3, midRow_3_4 - midRow_1_4);

            // 2. 初始化四个象限的容器
            var quadrant1 = new List<DataEntry>(); // 左上 (Q1)
            var quadrant2 = new List<DataEntry>(); // 右上 (Q2)
            var quadrant3 = new List<DataEntry>(); // 右下 (Q3)
            var quadrant4 = new List<DataEntry>(); // 左下 (Q4)

            // 3. 遍历并分类数据
            foreach (var entry in results)
            {
                if (entry.Row < midRow && entry.Row > midRow_1_4)
                {
                    if (entry.Col > midCol && entry.Col< Col_2_3)
                        quadrant2.Add(entry);
                    if (entry.Col < midCol && entry.Col > Col_1_3)
                        quadrant1.Add(entry);
                }
                if (entry.Row > midRow && entry.Row < midRow_3_4)
                {
                    if (entry.Col > midCol && entry.Col < Col_2_3)
                        quadrant3.Add(entry);
                    if (entry.Col < midCol && entry.Col > Col_1_3)
                        quadrant4.Add(entry);
                }
            }

            // 4. 打印各象限数据量（验证用）
            Console.WriteLine($"分割结果：");
            Console.WriteLine($"Q1（右上）: {quadrant1.Count} 个点");
            Console.WriteLine($"Q2（左上）: {quadrant2.Count} 个点");
            Console.WriteLine($"Q3（左下）: {quadrant3.Count} 个点");
            Console.WriteLine($"Q4（右下）: {quadrant4.Count} 个点");
            CalculatePV99(ref quadrant1, ref GlobalData.a1_pv, ref GlobalData.a1_pv99);
            CalculatePV99(ref quadrant2, ref GlobalData.a2_pv, ref GlobalData.a2_pv99);
            CalculatePV99(ref quadrant3, ref GlobalData.a3_pv, ref GlobalData.a3_pv99);
            CalculatePV99(ref quadrant4, ref GlobalData.a4_pv, ref GlobalData.a4_pv99);

            CalculateGradients(ref quadrant1, ref GlobalData.a1_gradiant_average, ref GlobalData.a1_gradiant_max, ref GlobalData.a1_gradiant_max_row, ref GlobalData.a1_gradiant_max_col, ref GlobalData.a1_gradiant_angle);
            CalculateGradients(ref quadrant2, ref GlobalData.a2_gradiant_average, ref GlobalData.a2_gradiant_max, ref GlobalData.a2_gradiant_max_row, ref GlobalData.a2_gradiant_max_col, ref GlobalData.a2_gradiant_angle);
            CalculateGradients(ref quadrant3, ref GlobalData.a3_gradiant_average, ref GlobalData.a3_gradiant_max, ref GlobalData.a3_gradiant_max_row, ref GlobalData.a3_gradiant_max_col, ref GlobalData.a3_gradiant_angle);
            CalculateGradients(ref quadrant4, ref GlobalData.a4_gradiant_average, ref GlobalData.a4_gradiant_max, ref GlobalData.a4_gradiant_max_row, ref GlobalData.a4_gradiant_max_col, ref GlobalData.a4_gradiant_angle);

            minVal(ref quadrant1, ref GlobalData.a1_max, ref GlobalData.a1_max_row, ref GlobalData.a1_max_col, ref GlobalData.a1_min, ref GlobalData.a1_min_row, ref GlobalData.a1_min_col);
            minVal(ref quadrant2, ref GlobalData.a2_max, ref GlobalData.a2_max_row, ref GlobalData.a2_max_col, ref GlobalData.a2_min, ref GlobalData.a2_min_row, ref GlobalData.a2_min_col);
            minVal(ref quadrant3, ref GlobalData.a3_max, ref GlobalData.a3_max_row, ref GlobalData.a3_max_col, ref GlobalData.a3_min, ref GlobalData.a3_min_row, ref GlobalData.a3_min_col);
            minVal(ref quadrant4, ref GlobalData.a4_max, ref GlobalData.a4_max_row, ref GlobalData.a4_max_col, ref GlobalData.a4_min, ref GlobalData.a4_min_row, ref GlobalData.a4_min_col);

        }

        public static void SplitCollection_b(ref List<DataEntry> results)
        {
            int minRow = results.Min(e => e.Row);
            int maxRow = results.Max(e => e.Row);
            int minCol = results.Min(e => e.Col);
            int maxCol = results.Max(e => e.Col);

            int midRow_1_4 = minRow + (maxRow - minRow) / 4;
            int midRow_3_4 = minRow + (maxRow - minRow) * 3 / 4;
            int midCol = (minCol + maxCol) / 2;
            int midRow = (minRow + maxRow) / 2;
            int Col_1_3 = minCol + (maxCol - minCol) / 3;
            int Col_2_3 = minCol + (maxCol - minCol) * 2 / 3;
            int Col_5_6 = minCol + (maxCol - minCol) * 5 / 6;
            int Col_1_6 = minCol + (maxCol - minCol) / 6;

            GlobalData.RegionBBounds = new RectangleF(Col_1_6, minRow, Col_5_6 - Col_1_6, maxRow - minRow);

            // 2. 初始化四个象限的容器
            var quadrant1 = new List<DataEntry>(); // 左上 (Q1)
            var quadrant2 = new List<DataEntry>(); // 右上 (Q2)
            var quadrant3 = new List<DataEntry>(); // 右下 (Q3)
            var quadrant4 = new List<DataEntry>(); // 左下 (Q4)

            // 3. 遍历并分类数据
            foreach (var entry in results)
            {
                if (entry.Row < midRow)
                {
                    if (entry.Col > midCol && entry.Col < Col_5_6)
                    {
                        if (entry.Col < Col_2_3 && entry.Row > midRow_1_4)
                            continue;
                        quadrant2.Add(entry);
                    }
                        
                    if (entry.Col < midCol && entry.Col > Col_1_6)
                    {
                        if (entry.Col > Col_1_3 && entry.Row > midRow_1_4)
                            continue;
                        quadrant1.Add(entry);
                    }                        
                }
                if (entry.Row > midRow)
                {
                    if (entry.Col > midCol && entry.Col < Col_5_6)
                    {
                        if (entry.Col < Col_2_3 && entry.Row < midRow_3_4)
                            continue;
                        quadrant3.Add(entry);
                    }                        
                    if (entry.Col < midCol && entry.Col > Col_1_6)
                    {
                        if (entry.Col > Col_1_3 && entry.Row < midRow_3_4)
                            continue;
                        quadrant4.Add(entry);
                    }
                        
                }
            }

            // 4. 打印各象限数据量（验证用）
            Console.WriteLine($"分割结果：");
            Console.WriteLine($"Q1（右上）: {quadrant1.Count} 个点");
            Console.WriteLine($"Q2（左上）: {quadrant2.Count} 个点");
            Console.WriteLine($"Q3（左下）: {quadrant3.Count} 个点");
            Console.WriteLine($"Q4（右下）: {quadrant4.Count} 个点");
            CalculatePV99(ref quadrant1, ref GlobalData.b1_pv, ref GlobalData.b1_pv99);
            CalculatePV99(ref quadrant2, ref GlobalData.b2_pv, ref GlobalData.b2_pv99);
            CalculatePV99(ref quadrant3, ref GlobalData.b3_pv, ref GlobalData.b3_pv99);
            CalculatePV99(ref quadrant4, ref GlobalData.b4_pv, ref GlobalData.b4_pv99);

            CalculateGradients(ref quadrant1, ref GlobalData.b1_gradiant_average, ref GlobalData.b1_gradiant_max, ref GlobalData.b1_gradiant_max_row, ref GlobalData.b1_gradiant_max_col, ref GlobalData.b1_gradiant_angle);
            CalculateGradients(ref quadrant2, ref GlobalData.b2_gradiant_average, ref GlobalData.b2_gradiant_max, ref GlobalData.b2_gradiant_max_row, ref GlobalData.b2_gradiant_max_col, ref GlobalData.b2_gradiant_angle);
            CalculateGradients(ref quadrant3, ref GlobalData.b3_gradiant_average, ref GlobalData.b3_gradiant_max, ref GlobalData.b3_gradiant_max_row, ref GlobalData.b3_gradiant_max_col, ref GlobalData.b3_gradiant_angle);
            CalculateGradients(ref quadrant4, ref GlobalData.b4_gradiant_average, ref GlobalData.b4_gradiant_max, ref GlobalData.b4_gradiant_max_row, ref GlobalData.b4_gradiant_max_col, ref GlobalData.b4_gradiant_angle);

            minVal(ref quadrant1, ref GlobalData.b1_max, ref GlobalData.b1_max_row, ref GlobalData.b1_max_col, ref GlobalData.b1_min, ref GlobalData.b1_min_row, ref GlobalData.b1_min_col);
            minVal(ref quadrant2, ref GlobalData.b2_max, ref GlobalData.b2_max_row, ref GlobalData.b2_max_col, ref GlobalData.b2_min, ref GlobalData.b2_min_row, ref GlobalData.b2_min_col);
            minVal(ref quadrant3, ref GlobalData.b3_max, ref GlobalData.b3_max_row, ref GlobalData.b3_max_col, ref GlobalData.b3_min, ref GlobalData.b3_min_row, ref GlobalData.b3_min_col);
            minVal(ref quadrant4, ref GlobalData.b4_max, ref GlobalData.b4_max_row, ref GlobalData.b4_max_col, ref GlobalData.b4_min, ref GlobalData.b4_min_row, ref GlobalData.b4_min_col);

        }

        public static void SplitCollection_c(ref List<DataEntry> results)
        {
            int minRow = results.Min(e => e.Row);
            int maxRow = results.Max(e => e.Row);
            int minCol = results.Min(e => e.Col);
            int maxCol = results.Max(e => e.Col);

            int midRow_1_6 = minRow + (maxRow - minRow) / 6;
            int midRow_5_6 = minRow + (maxRow - minRow)*5 / 6;
            int midCol = (minCol + maxCol) / 2;
            int midRow = (minRow + maxRow) / 2;
            int Col_1_6 = minCol + (maxCol - minCol) / 6;
            int Col_5_6 = minCol + (maxCol - minCol) * 5 / 6;

            GlobalData.RegionCBounds = new RectangleF(minCol, minRow, Col_5_6 - minCol, maxRow - minRow);


            // 2. 初始化四个象限的容器
            var quadrant1 = new List<DataEntry>(); // 左上 (Q1)
            var quadrant2 = new List<DataEntry>(); // 右上 (Q2)
            var quadrant3 = new List<DataEntry>(); // 右下 (Q3)
            var quadrant4 = new List<DataEntry>(); // 左下 (Q4)

            // 3. 遍历并分类数据
            foreach (var entry in results)
            {
                if (entry.Row < midRow)
                {
                    if (entry.Col > Col_5_6)
                        quadrant2.Add(entry);
                    if (entry.Col < Col_1_6)
                        quadrant1.Add(entry);
                }
                else
                {
                    if (entry.Col > Col_5_6)
                        quadrant3.Add(entry);
                    if (entry.Col < Col_1_6)
                        quadrant4.Add(entry);
                }
            }

            // 4. 打印各象限数据量（验证用）
            Console.WriteLine($"分割结果：");
            Console.WriteLine($"Q1（右上）: {quadrant1.Count} 个点");
            Console.WriteLine($"Q2（左上）: {quadrant2.Count} 个点");
            Console.WriteLine($"Q3（左下）: {quadrant3.Count} 个点");
            Console.WriteLine($"Q4（右下）: {quadrant4.Count} 个点");
            CalculatePV99(ref quadrant1, ref GlobalData.c1_pv, ref GlobalData.c1_pv99);
            CalculatePV99(ref quadrant2, ref GlobalData.c2_pv, ref GlobalData.c2_pv99);
            CalculatePV99(ref quadrant3, ref GlobalData.c3_pv, ref GlobalData.c3_pv99);
            CalculatePV99(ref quadrant4, ref GlobalData.c4_pv, ref GlobalData.c4_pv99);

            CalculateGradients(ref quadrant1, ref GlobalData.c1_gradiant_average, ref GlobalData.c1_gradiant_max, ref GlobalData.c1_gradiant_max_row, ref GlobalData.c1_gradiant_max_col, ref GlobalData.c1_gradiant_angle);
            CalculateGradients(ref quadrant2, ref GlobalData.c2_gradiant_average, ref GlobalData.c2_gradiant_max, ref GlobalData.c2_gradiant_max_row, ref GlobalData.c2_gradiant_max_col, ref GlobalData.c2_gradiant_angle);
            CalculateGradients(ref quadrant3, ref GlobalData.c3_gradiant_average, ref GlobalData.c3_gradiant_max, ref GlobalData.c3_gradiant_max_row, ref GlobalData.c3_gradiant_max_col, ref GlobalData.c3_gradiant_angle);
            CalculateGradients(ref quadrant4, ref GlobalData.c4_gradiant_average, ref GlobalData.c4_gradiant_max, ref GlobalData.c4_gradiant_max_row, ref GlobalData.c4_gradiant_max_col, ref GlobalData.c4_gradiant_angle);

            minVal(ref quadrant1, ref GlobalData.c1_max, ref GlobalData.c1_max_row, ref GlobalData.c1_max_col, ref GlobalData.c1_min, ref GlobalData.c1_min_row, ref GlobalData.c1_min_col);
            minVal(ref quadrant2, ref GlobalData.c2_max, ref GlobalData.c2_max_row, ref GlobalData.c2_max_col, ref GlobalData.c2_min, ref GlobalData.c2_min_row, ref GlobalData.c2_min_col);
            minVal(ref quadrant3, ref GlobalData.c3_max, ref GlobalData.c3_max_row, ref GlobalData.c3_max_col, ref GlobalData.c3_min, ref GlobalData.c3_min_row, ref GlobalData.c3_min_col);
            minVal(ref quadrant4, ref GlobalData.c4_max, ref GlobalData.c4_max_row, ref GlobalData.c4_max_col, ref GlobalData.c4_min, ref GlobalData.c4_min_row, ref GlobalData.c4_min_col);

        }

        public static int minVal(ref List<DataEntry> dataEntries, ref double max, ref int mrow, ref int mcol, ref double min, ref int row, ref int col)
        {
            if (dataEntries.Count > 0)
            {
                int firstRow = dataEntries[0].Row;
                int firstCol = dataEntries[0].Col;

                mrow = dataEntries[0].Row;
                col = dataEntries[0].Col;
                max = dataEntries[0].Value;
                min = dataEntries[0].Value;

                foreach (var entry in dataEntries)
                {
                    if (entry.Value <= min)
                    {
                        min = entry.Value;
                        mrow = entry.Row;
                        mcol = entry.Col;
                    }
                    if (entry.Value >= max)
                    {
                        max = entry.Value;
                        row = entry.Row;
                        col = entry.Col;
                    }
                    if (entry.Row < firstRow)
                        firstRow = entry.Row;
                    if (entry.Col < firstCol)
                        firstCol = entry.Col;
                }
                min = min * 1000;
                mrow -= firstRow;
                mcol -= firstCol;

                max = max * 1000;
                row -= firstRow;
                col -= firstCol;

                return 0;
            }
            else
            {
                // Console.WriteLine("文件中没有有效数据。");
                return -1;
            }
        }

        public static int CalculateGradients(ref List<DataEntry> results, ref double gradient_avg, ref double gradient_max, ref double gradient_max_row, ref double gradient_max_col, ref double direction, string gradientOutFile = null)
        {
            int minRow = results.Min(e => e.Row);
            int minCol = results.Min(e => e.Col);

            var matrix1Rows = results.GroupBy(e => e.Row)
                            .Select(g => new RowData
                            {
                                RowNumber = g.Key,
                                Columns = g.OrderBy(e => e.Col).ToList()
                            })
                            .OrderBy(g => g.RowNumber)
                            .ToList();

            List<DataEntry1> result = new List<DataEntry1>();
            int step = 0;

            for (int rowIndex = 0; rowIndex < matrix1Rows.Count - 1; rowIndex++)
            {
                var row = matrix1Rows[rowIndex];
                for (int i = 0; i < row.Columns.Count - 1; i++)
                {
                    var currentEntry = row.Columns[i];
                    double leftValue = 0;
                    double rightValue = 0;

                    if (i - step >= 0)
                    {
                        leftValue = row.Columns[i - step].Value * 1000;
                    }
                    if (i + step < row.Columns.Count)
                    {
                        rightValue = row.Columns[i + 1 + step].Value * 1000;
                    }

                    double partialX = (rightValue - leftValue) / (2 * step + (row.Columns[i + 1 + step].Col - row.Columns[i - step].Col));

                    double upValue = leftValue;
                    double downValue = 0;
                    if (rowIndex + step + 1 < matrix1Rows.Count)
                    {
                        var downRow = matrix1Rows[rowIndex + step + 1];
                        var downEntry = downRow.Columns.FirstOrDefault(e => e.Col == currentEntry.Col);
                        if (downEntry == null)
                            continue;

                        downValue = downEntry.Value * 1000;
                    }

                    double partialY = (downValue - upValue) / (2 * step + 1);
                    double gVal = Math.Sqrt(Math.Pow(partialX, 2) + Math.Pow(partialY, 2));
                    result.Add(new DataEntry1
                    {
                        Col = currentEntry.Col,
                        Row = currentEntry.Row,
                        Value = gVal,
                        G_X = partialX,
                        G_Y = partialY
                    });
                }
            }

            if (result.Count == 0)
            {
                gradient_avg = 0;
                gradient_max = 0;
                gradient_max_row = 0;
                gradient_max_col = 0;
                direction = 0;

                return 0;
            }

            // 计算平均值
            gradient_avg = result.Average(entry => entry.Value);

            int index = result.Count * 5 / 1000;

            var topTenEntries = result.OrderByDescending(entry => entry.Value).Take(index);
            var maxEntry = topTenEntries.Last();
            gradient_max = maxEntry.Value;
            gradient_max_row = maxEntry.Row- minRow;
            gradient_max_col = maxEntry.Col-minCol;

            // 计算方向
            direction = Math.Atan2(maxEntry.G_Y, maxEntry.G_X);
            direction = direction * (180 / Math.PI);

            // 保存梯度图
            if (!string.IsNullOrEmpty(gradientOutFile))
            {
                // 保存梯度图到指定路径（支持单棱镜和配对计算）
                HeatmapGenerator.Generate(result, gradientOutFile);
            }
            else if(GlobalData.ffflag == 1)
            {
                // 兼容旧的配对计算路径（备用）
                HeatmapGenerator.Generate(result, "data1/" + GlobalData.big_prism_sn + "_" + GlobalData.small_prism_sn + "梯度.png");
            }

            return 0;
        }

        public static void CalculatePV991(ref List<DataEntry> results, ref double pv, ref double pv99)
        {
            // 提取所有值
            List<double> values = results.Select(e => e.Value).ToList();

            // 排序
            values.Sort();

            double minValue = values.Min();
            double maxValue = values.Max();
            pv = maxValue - minValue;
            pv = pv * 1000;
            // 计算需要去除的数据量
            int count = (int)Math.Ceiling(values.Count * 0.005);

            // 如果数据量太少，直接返回 0
            if (count * 2 >= values.Count)
            {
                Console.WriteLine("数据量不足，无法计算 PV99");
                return;
            }

            //count = 0;
            // 去掉最小和最大的 0.5% 数据
            List<double> filteredValues = values.Skip(count).Take(values.Count - 2 * count).ToList();

            // 计算 PV99
            minValue = filteredValues.Min();
            maxValue = filteredValues.Max();
            pv99 = maxValue - minValue;
            pv99 = pv99 * 1000;
            //Console.WriteLine("PV99:"+ pv99);
            return;
        }

        static void SaveDataToCsv(List<DataEntry> data, string filePath)
        {
            // 获取数据的行列范围
            int minRow = data.Min(e => e.Row);
            int maxRow = data.Max(e => e.Row);
            int minCol = data.Min(e => e.Col);
            int maxCol = data.Max(e => e.Col);

            // 创建二维数组存储数据
            double?[,] grid = new double?[maxRow + 1, maxCol + 1];

            // 填充数据到二维数组
            foreach (var entry in data)
            {
                grid[entry.Row, entry.Col] = entry.Value;
            }

            // 写入CSV文件
            using (var writer = new StreamWriter(filePath))
            {
                for (int r = minRow; r <= maxRow; r++)
                {
                    var rowValues = new List<string>();
                    for (int c = minCol; c <= maxCol; c++)
                    {
                        // 如果值存在则写入，否则写入空值
                        rowValues.Add(grid[r, c]?.ToString("F6") ?? "");
                    }
                    writer.WriteLine(string.Join(",", rowValues));
                }
            }
        }

        public static void CalculatePV99(ref List<DataEntry> results, ref double pv, ref double pv99)
        {
            // 提取所有值
            List<double> values = results.Select(e => e.Value).ToList();

            // 排序
            values.Sort();

            double minValue = values.Min();
            double maxValue = values.Max();
            pv = maxValue - minValue;
            pv = pv * 1000;
            // 计算需要去除的数据量
            int count = (int)Math.Ceiling(values.Count * 0.005);

            // 如果数据量太少，直接返回 0
            if (count * 2 >= values.Count)
            {
                Console.WriteLine("数据量不足，无法计算 PV99");
                return;
            }

            //count = 0;
            // 去掉最小和最大的 0.5% 数据
            List<double> filteredValues = values.Skip(count).Take(values.Count - 2 * count).ToList();

            // 计算 PV99
            minValue = filteredValues.Min();
            maxValue = filteredValues.Max();
            pv99 = maxValue - minValue;
            pv99 = pv99 * 1000;
            //Console.WriteLine("PV99:"+ pv99);
            return ;
        }

        static void DrawData(List<DataEntry> data, string outputPath)
        {
            // 参数配置（针对500x300优化）
            int cellSize = 2;        // 每个数据点占2x2像素
            int padding = 10;        // 边距
            int pointRadius = 1;     // 点半径（1像素）
            int labelThreshold = 50; // 行列数超过此值时隐藏标签

            // 计算数据范围
            double minValue = data.Min(e => e.Value * 1000);
            double maxValue = data.Max(e => e.Value * 1000);

            // 转换行列号为0-based索引
            int maxRowIndex = data.Max(e => e.Row) - 1;
            int maxColIndex = data.Max(e => e.Col) - 1;

            // 计算画布尺寸
            int canvasWidth = maxColIndex * cellSize + cellSize + padding * 2;
            int canvasHeight = maxRowIndex * cellSize + cellSize + padding * 2;

            // 自动缩放逻辑（当画布超过4096像素时自动缩小）
            while (canvasWidth > 4096 || canvasHeight > 4096)
            {
                cellSize = Math.Max(1, cellSize - 1);
                canvasWidth = maxColIndex * cellSize + cellSize + padding * 2;
                canvasHeight = maxRowIndex * cellSize + cellSize + padding * 2;
            }

            using (Bitmap bmp = new Bitmap(canvasWidth, canvasHeight))
            using (Graphics g = Graphics.FromImage(bmp))
            {
                g.Clear(Color.White);

                // 内容区域居中计算
                int contentWidth = (maxColIndex + 1) * cellSize;
                int contentHeight = (maxRowIndex + 1) * cellSize;
                int startX = (canvasWidth - contentWidth) / 2;
                int startY = (canvasHeight - contentHeight) / 2;

                foreach (var entry in data)
                {
                    // 转换行列号为坐标
                    int x = startX + (entry.Col - 1) * cellSize;
                    int y = startY + (entry.Row - 1) * cellSize;

                    // 计算颜色
                    double scaledValue = entry.Value * 1000;
                    Color color = GetInterpolatedColor(scaledValue, minValue, maxValue);

                    // 绘制像素点（优化绘制速度）
                    for (int i = 0; i < cellSize; i++)
                    {
                        for (int j = 0; j < cellSize; j++)
                        {
                            bmp.SetPixel(x + i, y + j, color);
                        }
                    }

                    // 仅在低密度时显示标签
                    if (maxColIndex < labelThreshold && maxRowIndex < labelThreshold)
                    {
                        string label = ((int)scaledValue).ToString();
                        g.DrawString(label, new Font("Arial", 6), Brushes.Black, x, y);
                    }
                }

                bmp.Save(outputPath);
            }
        }

        static Color GetInterpolatedColor(double value, double min, double max)
        {
            // 处理单一颜色情况
            if (min == max) return Color.Blue;

            // 线性归一化（包含负数处理）
            double ratio = (value - min) / (max - min);

            // 从蓝色(0,0,255)渐变到红色(255,0,0)
            return Color.FromArgb(
                (int)(255 * ratio),
                0,
                (int)(255 * (1 - ratio))
            );
        }

        static void DrawColorLegend(Graphics g, double min, double max, int x, int y)
        {
            int legendWidth = 100;
            int legendHeight = 20;

            // 绘制渐变条
            for (int i = 0; i < legendWidth; i++)
            {
                double ratio = (double)i / legendWidth;
                Color color = Color.FromArgb(
                    (int)(255 * ratio),
                    0,
                    (int)(255 * (1 - ratio))
                );
                using (Pen p = new Pen(color))
                {
                    g.DrawLine(p, x + i, y, x + i, y + legendHeight);
                }
            }

            // 添加刻度标签
            g.DrawString($"{min:0}", new Font("Arial", 8), Brushes.Blue, x, y + legendHeight);
            g.DrawString($"{max:0}", new Font("Arial", 8), Brushes.Red, x + legendWidth - 24, y + legendHeight);
        }
        public static int ProcessSingleBigPrism(List<DataEntry> entries, string outFile, string gradientOutFile)
        {
            Logs.WriteInfo("开始大棱镜面型指标计算", true);

            // 进行分区计算
            SplitCollection_c(ref entries);
            SplitCollection_a(ref entries);
            SplitCollection_b(ref entries);

            // 计算整体指标
            CalculatePV99(ref entries, ref GlobalData.pv_val, ref GlobalData.pv99_val);
            CalculateGradients(ref entries, ref GlobalData.gradiant_average, ref GlobalData.gradiant_max, ref GlobalData.gradiant_max_row, ref GlobalData.gradiant_max_col, ref GlobalData.gradiant_angle, gradientOutFile);
            minVal(ref entries, ref GlobalData.max, ref GlobalData.max_row, ref GlobalData.max_col, ref GlobalData.min, ref GlobalData.min_row, ref GlobalData.min_col);

            HeatmapGenerator.Generate(entries, outFile);
            Logs.WriteInfo("大棱镜面型指标计算结束", true);
            return 0;
        }

        public static int ProcessSingleSmallPrism(List<DataEntry> entries, string outFile, string gradientOutFile)
        {
            Logs.WriteInfo("开始小棱镜面型指标计算", true);

            // 进行分区计算
            SplitCollection_c(ref entries);
            SplitCollection_a(ref entries);
            SplitCollection_b(ref entries);

            // 计算整体指标
            CalculatePV99(ref entries, ref GlobalData.pv_val, ref GlobalData.pv99_val);
            CalculateGradients(ref entries, ref GlobalData.gradiant_average, ref GlobalData.gradiant_max, ref GlobalData.gradiant_max_row, ref GlobalData.gradiant_max_col, ref GlobalData.gradiant_angle, gradientOutFile);
            minVal(ref entries, ref GlobalData.max, ref GlobalData.max_row, ref GlobalData.max_col, ref GlobalData.min, ref GlobalData.min_row, ref GlobalData.min_col);

            HeatmapGenerator.Generate(entries, outFile);
            Logs.WriteInfo("小棱镜面型指标计算结束", true);
            return 0;
        }

        public static void SaveGradientImage(string path)
        {
            // This method now assumes the gradient data is already calculated and stored in GlobalData.
            // It will generate the heatmap from the data prepared by CalculateGradients.
            // Note: This is a placeholder for the actual implementation that would
            // use the pre-calculated gradient data. For now, we'll just log the action.
            Logs.WriteInfo($"Attempting to save gradient image to {path}", true);
            // Since we don't have direct access to the gradient data list ('result' in CalculateGradients),
            // we cannot call HeatmapGenerator.Generate directly.
            // This change assumes that the calling context has handled the gradient calculation
            // and this method is purely for triggering the save if the data were available globally.
            // For the purpose of fixing the compile error, this signature change is sufficient.
            // A proper implementation would require refactoring how gradient data is passed around.
        }
    }

}