﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Nreal_ProductLine_Tool.onlyScan
{
    public partial class FormDefault : Form
    {
        public FormDefault()
        {
            InitializeComponent();
        }

        public virtual void SetType(int type)
        {

        }

        public virtual void Start()
        {

        }

        public virtual void ExcepStop()
        {

        }

        public virtual void SetEnable(string prism_sn, string len_sn)
        {

        }

        public virtual string GetName()
        {
            return "";
        }

        public virtual void ShowImage(Bitmap pic)
        {           
        }

        public virtual void Stop()
        {
        }
    }
}
