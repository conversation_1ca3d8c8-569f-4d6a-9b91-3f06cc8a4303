﻿using Nreal_ProductLine_Tool.onlyScan;
using System;
using System.IO;
using System.Net;
using System.Net.Sockets;
using System.Text;
using System.Threading;
using System.Windows.Forms;

public delegate void LinkStatus(string d_value);
public delegate void FinishNotify();

class SiriusServer
{
    private const int Port = 6000;
    private TcpListener server;
    public static bool isRunning;

    public LinkStatus linkStatus;
    public FinishNotify finishNotify;
    public static AutoResetEvent sendEvent = new AutoResetEvent(false);
    public static string dataToSend = string.Empty;

    public SiriusServer()
    {
        server = new TcpListener(IPAddress.Any, Port);
        isRunning = false;
    }
    
    public int Start()
    {
        try
        {
            server.Start();
            
            Console.WriteLine($"服务器已启动，监听端口 {Port}，等待客户端连接...");

            TcpClient client = server.AcceptTcpClient();
            Console.WriteLine("客户端已连接。");
            linkStatus("已连接");
            isRunning = true;
            // 获取网络流
            NetworkStream stream = client.GetStream();

            // 启动接收线程
            Thread receiveThread = new Thread(() => ReceiveData(stream));
            receiveThread.Start();

            // 启动发送线程
            Thread sendThread = new Thread(() => SendData(stream));
            sendThread.Start();

            // 等待线程结束
            receiveThread.Join();
            sendThread.Join();

            return 0;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"服务器启动出错: {ex.Message}");
            return -1;
        }
    }

    public void Stop()
    {
        isRunning = false;
        server.Stop();
        Console.WriteLine("服务器已停止。");
    }

    int CalPV(string result)
    {
        string[] parts = result.Split(',');
        if (parts.Length > 3)
        {
            int indexOfA = Array.IndexOf(parts, "A");
            if (indexOfA != -1 && indexOfA < parts.Length - 1)
            {
                double pv;
                if (double.TryParse(parts[indexOfA + 1], out pv))
                {
                    GlobalData.pv_val = pv;
                }
                else
                {
                    return -1;
                }
            }
            else
            {
                return -1;
            }
        }
        else
        {
            return -1;
        }

        return 0;
    }

    void ReceiveData(NetworkStream stream)
    {
        byte[] buffer = new byte[1024];
        while (true)
        {
            try
            {
                int bytesRead = stream.Read(buffer, 0, buffer.Length);
                if (bytesRead == 0)
                {
                    Console.WriteLine("客户端已断开连接。");
                    linkStatus("连接已断开");
                    isRunning = false;
                    break;
                }

                string receivedMessage = Encoding.UTF8.GetString(buffer, 0, bytesRead);
                if(receivedMessage.IndexOf("measurementFinished") >= 0)
                {
                    CalPV(receivedMessage);
                    finishNotify();
                }
                    
                //Console.WriteLine($"收到客户端消息：{receivedMessage}");
                Logs.WriteInfo(receivedMessage, true);
                //Logs.WriteInfo($"收到客户端消息长度：{receivedMessage.Length}", true);
            }
            catch (IOException)
            {
                Console.WriteLine("客户端异常断开连接。");
                break;
            }
            catch (SocketException)
            {
                Console.WriteLine("网络异常，连接已断开。");
                break;
            }
        }
    }

    void SendData(NetworkStream stream)
    {
        while (isRunning)
        {
            try
            {
                sendEvent.WaitOne();
                byte[] sendData = Encoding.UTF8.GetBytes(dataToSend);
                stream.Write(sendData, 0, sendData.Length);
            }
            catch (IOException)
            {
                Console.WriteLine("客户端异常断开连接。");
                break;
            }
            catch (SocketException)
            {
                Console.WriteLine("网络异常，连接已断开。");
                break;
            }
        }
    }

    private string ProcessMessage(string message)
    {
        switch (message.Trim())
        {
            case "<startMeasure>":
                // 模拟服务端响应客户端启动测量
                Console.WriteLine("向客户端发送开始测量响应");
                return "<MeasurementStarted>";
            case "<disableMeasurement_true>":
                // 模拟服务端响应客户端禁用测量按钮
                Console.WriteLine("向客户端发送禁用测量按钮响应");
                return "<MeasurementButtonDisabled>";
            case "<disableMeasurement_false>":
                // 模拟服务端响应客户端解除禁用测量按钮
                Console.WriteLine("向客户端发送解除禁用测量按钮响应");
                return "<MeasurementButtonEnabled>";
            default:
                return "[错误]:未知命令";
        }
    }
}
