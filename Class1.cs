﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;

namespace RectangleExtraction
{
    class Program
    {
        // 定义一个结构体来存储数据点
        struct DataPoint
        {
            public int x;
            public int y;
            public double value;
            public string line;
        }

        // 函数用于寻找最大矩形
        static (int, (int, int, int, int)) MaximalRectangle(int[,] matrix)
        {
            if (matrix.Length == 0)
            {
                return (0, (0, 0, 0, 0));
            }
            int m = matrix.GetLength(0);
            int n = matrix.GetLength(1);
            int maxArea = 0;
            (int, int, int, int) maxCoords = (0, 0, 0, 0);
            int[] heights = new int[n];
            int[] left = new int[n];
            int[] right = new int[n];
            for (int i = 0; i < n; i++)
            {
                right[i] = n;
            }

            for (int i = 0; i < m; i++)
            {
                // 更新高度数组
                for (int j = 0; j < n; j++)
                {
                    heights[j] = matrix[i, j] == 1 ? heights[j] + 1 : 0;
                }

                // 更新左边界数组
                int currentLeft = 0;
                for (int j = 0; j < n; j++)
                {
                    if (matrix[i, j] == 1)
                    {
                        left[j] = Math.Max(left[j], currentLeft);
                    }
                    else
                    {
                        left[j] = 0;
                        currentLeft = j + 1;
                    }
                }

                // 更新右边界数组
                int currentRight = n;
                for (int j = n - 1; j >= 0; j--)
                {
                    if (matrix[i, j] == 1)
                    {
                        right[j] = Math.Min(right[j], currentRight);
                    }
                    else
                    {
                        right[j] = n;
                        currentRight = j;
                    }
                }

                // 计算当前行的最大面积
                for (int j = 0; j < n; j++)
                {
                    int area = heights[j] * (right[j] - left[j]);
                    if (area > maxArea)
                    {
                        maxArea = area;
                        int topRow = i - heights[j] + 1;
                        int bottomRow = i;
                        int leftCol = left[j];
                        int rightCol = right[j] - 1;
                        maxCoords = (topRow, bottomRow, leftCol, rightCol);
                    }
                }
            }
            return (maxArea, maxCoords);
        }

        // 函数用于提取最大矩形区域内的数据
        static void ExtractLargestRectangle(string inputFile, string outputFile)
        {
            List<DataPoint> points = new List<DataPoint>();
            List<int> xs = new List<int>();
            List<int> ys = new List<int>();
            HashSet<int> uniqueXs = new HashSet<int>();
            HashSet<int> uniqueYs = new HashSet<int>();

            try
            {
                string[] lines = File.ReadAllLines(inputFile);
                foreach (string line in lines)
                {
                    string[] parts = line.Split(' ');
                    if (parts.Length >= 3 && int.TryParse(parts[0], out int x) && int.TryParse(parts[1], out int y) && double.TryParse(parts[2], out double val))
                    {
                        points.Add(new DataPoint { x = x, y = y, value = val, line = line });
                        uniqueXs.Add(x);
                        uniqueYs.Add(y);
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"无法打开输入文件: {inputFile}, 错误信息: {ex.Message}");
                return;
            }

            if (points.Count == 0)
            {
                Console.WriteLine("No valid data found.");
                return;
            }

            xs = uniqueXs.OrderBy(x => x).ToList();
            ys = uniqueYs.OrderBy(y => y).ToList();

            if (xs.Count == 0 || ys.Count == 0)
            {
                Console.WriteLine("No valid data points to form a rectangle.");
                return;
            }

            int[,] matrix = new int[xs.Count, ys.Count];
            Dictionary<int, int> xToI = new Dictionary<int, int>();
            Dictionary<int, int> yToJ = new Dictionary<int, int>();
            for (int i = 0; i < xs.Count; i++)
            {
                xToI[xs[i]] = i;
            }
            for (int j = 0; j < ys.Count; j++)
            {
                yToJ[ys[j]] = j;
            }

            foreach (DataPoint point in points)
            {
                int i = xToI[point.x];
                int j = yToJ[point.y];
                matrix[i, j] = 1;
            }

            (int maxArea, (int topRow, int bottomRow, int leftCol, int rightCol) maxCoords) = MaximalRectangle(matrix);
            if (maxArea == 0)
            {
                Console.WriteLine("No valid rectangle found.");
                return;
            }

            int xMin = xs[maxCoords.topRow];
            int xMax = xs[maxCoords.bottomRow];
            int yMin = ys[maxCoords.leftCol];
            int yMax = ys[maxCoords.rightCol];

            List<string> outputLines = new List<string>();
            foreach (DataPoint data in points)
            {
                if (xMin <= data.x && data.x <= xMax && yMin <= data.y && data.y <= yMax)
                {
                    outputLines.Add(data.line);
                }
            }

            try
            {
                File.WriteAllLines(outputFile, outputLines);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"无法打开输出文件: {outputFile}, 错误信息: {ex.Message}");
                return;
            }

            Console.WriteLine($"Largest rectangle area: {maxArea}");
            Console.WriteLine($"Output saved to {outputFile}");
        }

        public static void ConvertFormat(string inputFile, string outputFile)
        {
            ExtractLargestRectangle(inputFile, outputFile);
        }
    }
}