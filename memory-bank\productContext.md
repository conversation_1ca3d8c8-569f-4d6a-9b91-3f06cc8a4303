# Product Context

This file provides a high-level overview of the project and the expected product that will be created. Initially it is based upon projectBrief.md (if provided) and all other available project-related information in the working directory. This file is intended to be updated as the project evolves, and should be used to inform all other modes of the project's goals and context.
2025-07-03 09:26:17 - Log of updates made will be appended as footnotes to the end of this file.

*

## Project Goal

*   

## Key Features

*   支持单棱镜独立计算和双棱镜配对拟合计算模式。
*   **自动化分析图像生成**: 在每次成功计算后，系统会自动生成一张高分辨率（1920x1080）的 PNG 图像。这张图像是对计算结果的可视化总结，旨在帮助用户快速、直观地理解分析结果。图像内容包括：
    *   **数据热力图**: 整体数据的可视化分布，颜色从冷色调（低值）到暖色调（高值）过渡。
    *   **关键区域**: 在图上标出核心分析区域（A, B, C区）。
    *   **核心指标**: 明确标注出数据中的最大/最小值点、最大梯度点及其方向，使用户能一眼定位到最需要关注的数据点。

## Overall Architecture

*   