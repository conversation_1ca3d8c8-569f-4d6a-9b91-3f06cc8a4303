干涉仪获取面型模拟数据并做对应的计算和数据上传

## 高级图像生成功能

### 1. 功能概述

`AdvancedImageGenerator` 是一个静态类，用于在数据计算分析完成后，自动生成一张高分辨率（1920x1080）的带注释的 PNG 格式图像。这张图像以热力图的形式直观地展示了计算结果的各项关键指标，便于快速诊断和分析。

### 2. 触发机制

该功能在主计算流程完成后被自动调用。系统会收集所有必要的计算指标和数据点，并将其传递给 `AdvancedImageGenerator.Generate` 方法来创建图像。

### 3. 图像内容详解

生成的图像包含以下几个核心部分：

*   **数据热力图 (Heatmap)**:
    *   整个背景是由所有数据点构成的热力图。
    *   颜色从蓝色渐变到红色，分别代表数据点从最小值到最大值的分布。

*   **区域划分 (Regions)**:
    *   图像上会用虚线标出预设的 A、B、C 三个关键区域的边界。
    *   每个区域旁边都有对应的字母标识。

*   **关键点注释 (Annotations)**:
    *   **最大值点**: 使用一个 **红色** 圆点标记，并显示如 `Max: 1.234` 的数值。
    *   **最小值点**: 使用一个 **蓝色** 圆点标记，并显示如 `Min: -0.987` 的数值。
    *   **最大梯度点**: 使用一个 **紫色** 方块标记，并显示如 `Max Grad: 0.567` 的数值。

*   **梯度方向 (Gradient Direction)**:
    *   从最大梯度点的位置会引出一个 **紫色箭头**，该箭头的方向明确指示了梯度的方向。

此图像为分析和调试提供了全面的可视化支持。
