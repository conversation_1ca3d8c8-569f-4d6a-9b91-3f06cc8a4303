﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;
using System.Xml;
using System.IO;
using ParamManager;

namespace Nreal_ProductLine_Tool.onlyScan
{
    internal class Configure
    {
        public List<string> database = new List<string>();

        public static string mes_resource = "NONE";
        public static int sn_length;
        public static int prism_sn_length;
        public static string file_saved_path;
        public static int wait_delay_time;

        public static int range_numbers;
        public static int historic_numbers;

        public static double D_MIN_PV;
        public static double D_MAN_PV;
        public static double X_MIN_PV;
        public static double X_MAN_PV;
        public static double Y_MIN_PV;
        public static double Y_MAN_PV;

        

        public Configure()
        {
            //getValueFromConf();
        }

        public static void Init_test(string sysFilePath)
        {
            Logs.WriteDebug("just for test " + sysFilePath, true);
        }

        public static void UpdateStationDataFunc(string stationNo, string projectName, string mode)
        {
            string tt = $" #站号->{stationNo} #项目->{projectName} #模式->{mode}";
        }

        public static void Init()
        {
            PM pm;
            pm = new PM();
            pm.updateStationData += UpdateStationDataFunc;
            pm.InitAll(true, 31, 0);

            Para_get(ref pm);
        }

        public static int Init_Local()
        {
            PM pm;
            pm = new PM();
            pm.updateStationData += UpdateStationDataFunc;
            int res = pm.ReadConfigFile();
            if (res != 0)
                return res;

            Para_get(ref pm);

            return 0;
        }

        public static void Para_get(ref PM pm)
        {
            range_numbers = pm.Vi("Product#range_numbers");
            historic_numbers = pm.Vi("Product#historic_numbers");

            D_MIN_PV = pm.Vi("Product#D_MIN_PV");
            D_MAN_PV = pm.Vi("Product#D_MAN_PV");
            X_MIN_PV = pm.Vi("Product#X_MIN_PV");
            X_MAN_PV = pm.Vi("Product#X_MAN_PV");
            Y_MIN_PV = pm.Vi("Product#Y_MIN_PV");
            Y_MAN_PV = pm.Vi("Product#Y_MAN_PV");
        }
    }

    public class GlobalData
    {
        public static int ffflag = 0;
        public static string big_prism_sn = "";
        public static string small_prism_sn = "";

        public static string big_prism_fname = "";
        public static string small_prism_fname = "";

        public static double x_value = 0;
        public static double y_value = 0;
        public static double diff = 0;

        public static double pv_val = 0;
        public static double pv99_val = 0;

        public static double gradiant_average = 0;
        public static double gradiant_max = 0;
        public static double gradiant_max_row = 0;
        public static double gradiant_max_col = 0;
        public static double gradiant_angle = 0;

        public static double max = 0;
        public static double min = 0;
        public static int max_row = 0;
        public static int max_col = 0;
        public static int min_row = 0;
        public static int min_col = 0;


        public static double d_pv = 0;
        public static double d_pv99 = 0;

        public static double x_pv = 0;
        public static double x_pv99 = 0;

        public static double a1_pv = 0;
        public static double a1_pv99 = 0;
        public static double a2_pv = 0;
        public static double a2_pv99 = 0;
        public static double a3_pv = 0;
        public static double a3_pv99 = 0;
        public static double a4_pv = 0;
        public static double a4_pv99 = 0;

        public static double a1_gradiant_average = 0;
        public static double a1_gradiant_max = 0;
        public static double a1_gradiant_max_row = 0;
        public static double a1_gradiant_max_col = 0;
        public static double a1_gradiant_angle = 0;
        public static double a2_gradiant_average = 0;
        public static double a2_gradiant_max = 0;
        public static double a2_gradiant_max_row = 0;
        public static double a2_gradiant_max_col = 0;
        public static double a2_gradiant_angle = 0;
        public static double a3_gradiant_average = 0;
        public static double a3_gradiant_max = 0;
        public static double a3_gradiant_max_row = 0;
        public static double a3_gradiant_max_col = 0;
        public static double a3_gradiant_angle = 0;
        public static double a4_gradiant_average = 0;
        public static double a4_gradiant_max = 0;
        public static double a4_gradiant_max_row = 0;
        public static double a4_gradiant_max_col = 0;
        public static double a4_gradiant_angle = 0;

        public static double a1_max = 0;
        public static double a1_min = 0;
        public static int a1_max_row = 0;
        public static int a1_max_col = 0;
        public static int a1_min_row = 0;
        public static int a1_min_col = 0;
        public static double a2_max = 0;
        public static double a2_min = 0;
        public static int a2_max_row = 0;
        public static int a2_max_col = 0;
        public static int a2_min_row = 0;
        public static int a2_min_col = 0;
        public static double a3_max = 0;
        public static double a3_min = 0;
        public static int a3_max_row = 0;
        public static int a3_max_col = 0;
        public static int a3_min_row = 0;
        public static int a3_min_col = 0;
        public static double a4_max = 0;
        public static double a4_min = 0;
        public static int a4_max_row = 0;
        public static int a4_max_col = 0;
        public static int a4_min_row = 0;
        public static int a4_min_col = 0;

        public static double b1_pv = 0;
        public static double b1_pv99 = 0;
        public static double b2_pv = 0;
        public static double b2_pv99 = 0;
        public static double b3_pv = 0;
        public static double b3_pv99 = 0;
        public static double b4_pv = 0;
        public static double b4_pv99 = 0;

        public static double b1_gradiant_average = 0;
        public static double b1_gradiant_max = 0;
        public static double b1_gradiant_max_row = 0;
        public static double b1_gradiant_max_col = 0;
        public static double b1_gradiant_angle = 0;
        public static double b2_gradiant_average = 0;
        public static double b2_gradiant_max = 0;
        public static double b2_gradiant_max_row = 0;
        public static double b2_gradiant_max_col = 0;
        public static double b2_gradiant_angle = 0;
        public static double b3_gradiant_average = 0;
        public static double b3_gradiant_max = 0;
        public static double b3_gradiant_max_row = 0;
        public static double b3_gradiant_max_col = 0;
        public static double b3_gradiant_angle = 0;
        public static double b4_gradiant_average = 0;
        public static double b4_gradiant_max = 0;
        public static double b4_gradiant_max_row = 0;
        public static double b4_gradiant_max_col = 0;
        public static double b4_gradiant_angle = 0;

        public static double b1_max = 0;
        public static double b1_min = 0;
        public static int b1_max_row = 0;
        public static int b1_max_col = 0;
        public static int b1_min_row = 0;
        public static int b1_min_col = 0;
        public static double b2_max = 0;
        public static double b2_min = 0;
        public static int b2_max_row = 0;
        public static int b2_max_col = 0;
        public static int b2_min_row = 0;
        public static int b2_min_col = 0;
        public static double b3_max = 0;
        public static double b3_min = 0;
        public static int b3_max_row = 0;
        public static int b3_max_col = 0;
        public static int b3_min_row = 0;
        public static int b3_min_col = 0;
        public static double b4_max = 0;
        public static double b4_min = 0;
        public static int b4_max_row = 0;
        public static int b4_max_col = 0;
        public static int b4_min_row = 0;
        public static int b4_min_col = 0;

        public static double c1_pv = 0;
        public static double c1_pv99 = 0;
        public static double c2_pv = 0;
        public static double c2_pv99 = 0;
        public static double c3_pv = 0;
        public static double c3_pv99 = 0;
        public static double c4_pv = 0;
        public static double c4_pv99 = 0;

        public static double c1_gradiant_average = 0;
        public static double c1_gradiant_max = 0;
        public static double c1_gradiant_max_row = 0;
        public static double c1_gradiant_max_col = 0;
        public static double c1_gradiant_angle = 0;
        public static double c2_gradiant_average = 0;
        public static double c2_gradiant_max = 0;
        public static double c2_gradiant_max_row = 0;
        public static double c2_gradiant_max_col = 0;
        public static double c2_gradiant_angle = 0;
        public static double c3_gradiant_average = 0;
        public static double c3_gradiant_max = 0;
        public static double c3_gradiant_max_row = 0;
        public static double c3_gradiant_max_col = 0;
        public static double c3_gradiant_angle = 0;
        public static double c4_gradiant_average = 0;
        public static double c4_gradiant_max = 0;
        public static double c4_gradiant_max_row = 0;
        public static double c4_gradiant_max_col = 0;
        public static double c4_gradiant_angle = 0;

        public static double c1_max = 0;
        public static double c1_min = 0;
        public static int c1_max_row = 0;
        public static int c1_max_col = 0;
        public static int c1_min_row = 0;
        public static int c1_min_col = 0;
        public static double c2_max = 0;
        public static double c2_min = 0;
        public static int c2_max_row = 0;
        public static int c2_max_col = 0;
        public static int c2_min_row = 0;
        public static int c2_min_col = 0;
        public static double c3_max = 0;
        public static double c3_min = 0;
        public static int c3_max_row = 0;
        public static int c3_max_col = 0;
        public static int c3_min_row = 0;
        public static int c3_min_col = 0;
        public static double c4_max = 0;
        public static double c4_min = 0;
        public static int c4_max_row = 0;
        public static int c4_max_col = 0;
        public static int c4_min_row = 0;
        public static int c4_min_col = 0;

    }
}
