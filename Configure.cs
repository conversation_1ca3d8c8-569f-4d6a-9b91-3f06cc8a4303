﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;
using System.Xml;
using System.IO;
using ParamManager;
using System.Drawing;

namespace Nreal_ProductLine_Tool.onlyScan
{
    internal class Configure
    {
        public List<string> database = new List<string>();

        public static string mes_resource = "NONE";
        public static int sn_length;
        public static int prism_sn_length;
        public static string file_saved_path;
        public static int wait_delay_time;

        public static int range_numbers;
        public static int historic_numbers;

        public static double D_MIN_PV;
        public static double D_MAN_PV;
        public static double X_MIN_PV;
        public static double X_MAN_PV;
        public static double Y_MIN_PV;
        public static double Y_MAN_PV;

        

        public Configure()
        {
            //getValueFromConf();
        }

        public static void Init_test(string sysFilePath)
        {
            Logs.WriteDebug("just for test " + sysFilePath, true);
        }

        public static void UpdateStationDataFunc(string stationNo, string projectName, string mode)
        {
            string tt = $" #站号->{stationNo} #项目->{projectName} #模式->{mode}";
        }

        public static void Init()
        {
            PM pm;
            pm = new PM();
            pm.updateStationData += UpdateStationDataFunc;
            pm.InitAll(true, 31, 0);

            Para_get(ref pm);
        }

        public static int Init_Local()
        {
            PM pm;
            pm = new PM();
            pm.updateStationData += UpdateStationDataFunc;
            int res = pm.ReadConfigFile();
            if (res != 0)
                return res;

            Para_get(ref pm);

            return 0;
        }

        public static void Para_get(ref PM pm)
        {
            range_numbers = pm.Vi("Product#range_numbers");
            historic_numbers = pm.Vi("Product#historic_numbers");

            D_MIN_PV = pm.Vi("Product#D_MIN_PV");
            D_MAN_PV = pm.Vi("Product#D_MAN_PV");
            X_MIN_PV = pm.Vi("Product#X_MIN_PV");
            X_MAN_PV = pm.Vi("Product#X_MAN_PV");
            Y_MIN_PV = pm.Vi("Product#Y_MIN_PV");
            Y_MAN_PV = pm.Vi("Product#Y_MAN_PV");
        }
    }
    
    public class CalculationMetrics
    {
        // Overall Metrics
        public double pv99_val { get; set; }
        public double gradiant_average { get; set; }
        public double gradiant_max { get; set; }
        public double gradiant_max_row { get; set; }
        public double gradiant_max_col { get; set; }
        public double gradiant_angle { get; set; }
        public double max { get; set; }
        public double min { get; set; }
        public int max_row { get; set; }
        public int max_col { get; set; }
        public int min_row { get; set; }
        public int min_col { get; set; }

        // Region A Metrics
        public double a1_pv99 { get; set; }
        public double a2_pv99 { get; set; }
        public double a3_pv99 { get; set; }
        public double a4_pv99 { get; set; }

        // Region B Metrics
        public double b1_pv99 { get; set; }
        public double b2_pv99 { get; set; }
        public double b3_pv99 { get; set; }
        public double b4_pv99 { get; set; }

        // Region C Metrics
        public double c1_pv99 { get; set; }
        public double c2_pv99 { get; set; }
        public double c3_pv99 { get; set; }
        public double c4_pv99 { get; set; }
        
        // Region boundary rectangles
        public RectangleF RegionABounds { get; set; }
        public RectangleF RegionBBounds { get; set; }
        public RectangleF RegionCBounds { get; set; }

        // Region A extreme values and gradients
        public double a1_max { get; set; }
        public double a1_min { get; set; }
        public double a1_max_row { get; set; }
        public double a1_max_col { get; set; }
        public double a1_min_row { get; set; }
        public double a1_min_col { get; set; }
        public double a1_gradiant_max { get; set; }
        public double a1_gradiant_max_row { get; set; }
        public double a1_gradiant_max_col { get; set; }

        public double a2_max { get; set; }
        public double a2_min { get; set; }
        public double a2_max_row { get; set; }
        public double a2_max_col { get; set; }
        public double a2_min_row { get; set; }
        public double a2_min_col { get; set; }
        public double a2_gradiant_max { get; set; }
        public double a2_gradiant_max_row { get; set; }
        public double a2_gradiant_max_col { get; set; }

        public double a3_max { get; set; }
        public double a3_min { get; set; }
        public double a3_max_row { get; set; }
        public double a3_max_col { get; set; }
        public double a3_min_row { get; set; }
        public double a3_min_col { get; set; }
        public double a3_gradiant_max { get; set; }
        public double a3_gradiant_max_row { get; set; }
        public double a3_gradiant_max_col { get; set; }

        public double a4_max { get; set; }
        public double a4_min { get; set; }
        public double a4_max_row { get; set; }
        public double a4_max_col { get; set; }
        public double a4_min_row { get; set; }
        public double a4_min_col { get; set; }
        public double a4_gradiant_max { get; set; }
        public double a4_gradiant_max_row { get; set; }
        public double a4_gradiant_max_col { get; set; }

        // Region B extreme values and gradients
        public double b1_max { get; set; }
        public double b1_min { get; set; }
        public double b1_max_row { get; set; }
        public double b1_max_col { get; set; }
        public double b1_min_row { get; set; }
        public double b1_min_col { get; set; }
        public double b1_gradiant_max { get; set; }
        public double b1_gradiant_max_row { get; set; }
        public double b1_gradiant_max_col { get; set; }

        public double b2_max { get; set; }
        public double b2_min { get; set; }
        public double b2_max_row { get; set; }
        public double b2_max_col { get; set; }
        public double b2_min_row { get; set; }
        public double b2_min_col { get; set; }
        public double b2_gradiant_max { get; set; }
        public double b2_gradiant_max_row { get; set; }
        public double b2_gradiant_max_col { get; set; }

        public double b3_max { get; set; }
        public double b3_min { get; set; }
        public double b3_max_row { get; set; }
        public double b3_max_col { get; set; }
        public double b3_min_row { get; set; }
        public double b3_min_col { get; set; }
        public double b3_gradiant_max { get; set; }
        public double b3_gradiant_max_row { get; set; }
        public double b3_gradiant_max_col { get; set; }

        public double b4_max { get; set; }
        public double b4_min { get; set; }
        public double b4_max_row { get; set; }
        public double b4_max_col { get; set; }
        public double b4_min_row { get; set; }
        public double b4_min_col { get; set; }
        public double b4_gradiant_max { get; set; }
        public double b4_gradiant_max_row { get; set; }
        public double b4_gradiant_max_col { get; set; }

        // Region C extreme values and gradients
        public double c1_max { get; set; }
        public double c1_min { get; set; }
        public double c1_max_row { get; set; }
        public double c1_max_col { get; set; }
        public double c1_min_row { get; set; }
        public double c1_min_col { get; set; }
        public double c1_gradiant_max { get; set; }
        public double c1_gradiant_max_row { get; set; }
        public double c1_gradiant_max_col { get; set; }

        public double c2_max { get; set; }
        public double c2_min { get; set; }
        public double c2_max_row { get; set; }
        public double c2_max_col { get; set; }
        public double c2_min_row { get; set; }
        public double c2_min_col { get; set; }
        public double c2_gradiant_max { get; set; }
        public double c2_gradiant_max_row { get; set; }
        public double c2_gradiant_max_col { get; set; }

        public double c3_max { get; set; }
        public double c3_min { get; set; }
        public double c3_max_row { get; set; }
        public double c3_max_col { get; set; }
        public double c3_min_row { get; set; }
        public double c3_min_col { get; set; }
        public double c3_gradiant_max { get; set; }
        public double c3_gradiant_max_row { get; set; }
        public double c3_gradiant_max_col { get; set; }

        public double c4_max { get; set; }
        public double c4_min { get; set; }
        public double c4_max_row { get; set; }
        public double c4_max_col { get; set; }
        public double c4_min_row { get; set; }
        public double c4_min_col { get; set; }
        public double c4_gradiant_max { get; set; }
        public double c4_gradiant_max_row { get; set; }
        public double c4_gradiant_max_col { get; set; }
    }

    public class ImageGenerationData
    {
        public List<DataEntry> AllPoints { get; set; }
        public CalculationMetrics Metrics { get; set; }
        public string OutputPath { get; set; }
    }

    public class GlobalData
    {
        // Boundaries for visualization
        public static RectangleF RegionABounds { get; set; }
        public static RectangleF RegionBBounds { get; set; }
        public static RectangleF RegionCBounds { get; set; }

        public static int ffflag = 0;
        public static string big_prism_sn = "";
        public static string small_prism_sn = "";

        public static string big_prism_fname = "";
        public static string small_prism_fname = "";

        public static double x_value = 0;
        public static double y_value = 0;
        public static double diff = 0;

        public static double pv_val = 0;
        public static double pv99_val = 0;

        public static double gradiant_average = 0;
        public static double gradiant_max = 0;
        public static double gradiant_max_row = 0;
        public static double gradiant_max_col = 0;
        public static double gradiant_angle = 0;

        public static double max = 0;
        public static double min = 0;
        public static int max_row = 0;
        public static int max_col = 0;
        public static int min_row = 0;
        public static int min_col = 0;


        public static double d_pv = 0;
        public static double d_pv99 = 0;

        public static double x_pv = 0;
        public static double x_pv99 = 0;

        public static double a1_pv = 0;
        public static double a1_pv99 = 0;
        public static double a2_pv = 0;
        public static double a2_pv99 = 0;
        public static double a3_pv = 0;
        public static double a3_pv99 = 0;
        public static double a4_pv = 0;
        public static double a4_pv99 = 0;

        public static double a1_gradiant_average = 0;
        public static double a1_gradiant_max = 0;
        public static double a1_gradiant_max_row = 0;
        public static double a1_gradiant_max_col = 0;
        public static double a1_gradiant_angle = 0;
        public static double a2_gradiant_average = 0;
        public static double a2_gradiant_max = 0;
        public static double a2_gradiant_max_row = 0;
        public static double a2_gradiant_max_col = 0;
        public static double a2_gradiant_angle = 0;
        public static double a3_gradiant_average = 0;
        public static double a3_gradiant_max = 0;
        public static double a3_gradiant_max_row = 0;
        public static double a3_gradiant_max_col = 0;
        public static double a3_gradiant_angle = 0;
        public static double a4_gradiant_average = 0;
        public static double a4_gradiant_max = 0;
        public static double a4_gradiant_max_row = 0;
        public static double a4_gradiant_max_col = 0;
        public static double a4_gradiant_angle = 0;

        public static double a1_max = 0;
        public static double a1_min = 0;
        public static int a1_max_row = 0;
        public static int a1_max_col = 0;
        public static int a1_min_row = 0;
        public static int a1_min_col = 0;
        public static double a2_max = 0;
        public static double a2_min = 0;
        public static int a2_max_row = 0;
        public static int a2_max_col = 0;
        public static int a2_min_row = 0;
        public static int a2_min_col = 0;
        public static double a3_max = 0;
        public static double a3_min = 0;
        public static int a3_max_row = 0;
        public static int a3_max_col = 0;
        public static int a3_min_row = 0;
        public static int a3_min_col = 0;
        public static double a4_max = 0;
        public static double a4_min = 0;
        public static int a4_max_row = 0;
        public static int a4_max_col = 0;
        public static int a4_min_row = 0;
        public static int a4_min_col = 0;

        public static double b1_pv = 0;
        public static double b1_pv99 = 0;
        public static double b2_pv = 0;
        public static double b2_pv99 = 0;
        public static double b3_pv = 0;
        public static double b3_pv99 = 0;
        public static double b4_pv = 0;
        public static double b4_pv99 = 0;

        public static double b1_gradiant_average = 0;
        public static double b1_gradiant_max = 0;
        public static double b1_gradiant_max_row = 0;
        public static double b1_gradiant_max_col = 0;
        public static double b1_gradiant_angle = 0;
        public static double b2_gradiant_average = 0;
        public static double b2_gradiant_max = 0;
        public static double b2_gradiant_max_row = 0;
        public static double b2_gradiant_max_col = 0;
        public static double b2_gradiant_angle = 0;
        public static double b3_gradiant_average = 0;
        public static double b3_gradiant_max = 0;
        public static double b3_gradiant_max_row = 0;
        public static double b3_gradiant_max_col = 0;
        public static double b3_gradiant_angle = 0;
        public static double b4_gradiant_average = 0;
        public static double b4_gradiant_max = 0;
        public static double b4_gradiant_max_row = 0;
        public static double b4_gradiant_max_col = 0;
        public static double b4_gradiant_angle = 0;

        public static double b1_max = 0;
        public static double b1_min = 0;
        public static int b1_max_row = 0;
        public static int b1_max_col = 0;
        public static int b1_min_row = 0;
        public static int b1_min_col = 0;
        public static double b2_max = 0;
        public static double b2_min = 0;
        public static int b2_max_row = 0;
        public static int b2_max_col = 0;
        public static int b2_min_row = 0;
        public static int b2_min_col = 0;
        public static double b3_max = 0;
        public static double b3_min = 0;
        public static int b3_max_row = 0;
        public static int b3_max_col = 0;
        public static int b3_min_row = 0;
        public static int b3_min_col = 0;
        public static double b4_max = 0;
        public static double b4_min = 0;
        public static int b4_max_row = 0;
        public static int b4_max_col = 0;
        public static int b4_min_row = 0;
        public static int b4_min_col = 0;

        public static double c1_pv = 0;
        public static double c1_pv99 = 0;
        public static double c2_pv = 0;
        public static double c2_pv99 = 0;
        public static double c3_pv = 0;
        public static double c3_pv99 = 0;
        public static double c4_pv = 0;
        public static double c4_pv99 = 0;

        public static double c1_gradiant_average = 0;
        public static double c1_gradiant_max = 0;
        public static double c1_gradiant_max_row = 0;
        public static double c1_gradiant_max_col = 0;
        public static double c1_gradiant_angle = 0;
        public static double c2_gradiant_average = 0;
        public static double c2_gradiant_max = 0;
        public static double c2_gradiant_max_row = 0;
        public static double c2_gradiant_max_col = 0;
        public static double c2_gradiant_angle = 0;
        public static double c3_gradiant_average = 0;
        public static double c3_gradiant_max = 0;
        public static double c3_gradiant_max_row = 0;
        public static double c3_gradiant_max_col = 0;
        public static double c3_gradiant_angle = 0;
        public static double c4_gradiant_average = 0;
        public static double c4_gradiant_max = 0;
        public static double c4_gradiant_max_row = 0;
        public static double c4_gradiant_max_col = 0;
        public static double c4_gradiant_angle = 0;

        public static double c1_max = 0;
        public static double c1_min = 0;
        public static int c1_max_row = 0;
        public static int c1_max_col = 0;
        public static int c1_min_row = 0;
        public static int c1_min_col = 0;
        public static double c2_max = 0;
        public static double c2_min = 0;
        public static int c2_max_row = 0;
        public static int c2_max_col = 0;
        public static int c2_min_row = 0;
        public static int c2_min_col = 0;
        public static double c3_max = 0;
        public static double c3_min = 0;
        public static int c3_max_row = 0;
        public static int c3_max_col = 0;
        public static int c3_min_row = 0;
        public static int c3_min_col = 0;
        public static double c4_max = 0;
        public static double c4_min = 0;
        public static int c4_max_row = 0;
        public static int c4_max_col = 0;
        public static int c4_min_row = 0;
        public static int c4_min_col = 0;

        /// <summary>
        /// Creates a snapshot of the current metrics in a thread-safe manner.
        /// </summary>
        /// <returns>A new CalculationMetrics instance with the current data.</returns>
        public static CalculationMetrics GetMetricsSnapshot()
        {
            return new CalculationMetrics
            {
                pv99_val = pv99_val,
                gradiant_average = gradiant_average,
                gradiant_max = gradiant_max,
                gradiant_max_row = gradiant_max_row,
                gradiant_max_col = gradiant_max_col,
                gradiant_angle = gradiant_angle,
                max = max,
                min = min,
                max_row = max_row,
                max_col = max_col,
                min_row = min_row,
                min_col = min_col,
                a1_pv99 = a1_pv99,
                a2_pv99 = a2_pv99,
                a3_pv99 = a3_pv99,
                a4_pv99 = a4_pv99,
                b1_pv99 = b1_pv99,
                b2_pv99 = b2_pv99,
                b3_pv99 = b3_pv99,
                b4_pv99 = b4_pv99,
                c1_pv99 = c1_pv99,
                c2_pv99 = c2_pv99,
                c3_pv99 = c3_pv99,
                c4_pv99 = c4_pv99,

                // Region boundaries
                RegionABounds = GlobalData.RegionABounds,
                RegionBBounds = GlobalData.RegionBBounds,
                RegionCBounds = GlobalData.RegionCBounds,

                // Region A extreme values and gradients
                a1_max = a1_max,
                a1_min = a1_min,
                a1_max_row = a1_max_row,
                a1_max_col = a1_max_col,
                a1_min_row = a1_min_row,
                a1_min_col = a1_min_col,
                a1_gradiant_max = a1_gradiant_max,
                a1_gradiant_max_row = a1_gradiant_max_row,
                a1_gradiant_max_col = a1_gradiant_max_col,

                a2_max = a2_max,
                a2_min = a2_min,
                a2_max_row = a2_max_row,
                a2_max_col = a2_max_col,
                a2_min_row = a2_min_row,
                a2_min_col = a2_min_col,
                a2_gradiant_max = a2_gradiant_max,
                a2_gradiant_max_row = a2_gradiant_max_row,
                a2_gradiant_max_col = a2_gradiant_max_col,

                a3_max = a3_max,
                a3_min = a3_min,
                a3_max_row = a3_max_row,
                a3_max_col = a3_max_col,
                a3_min_row = a3_min_row,
                a3_min_col = a3_min_col,
                a3_gradiant_max = a3_gradiant_max,
                a3_gradiant_max_row = a3_gradiant_max_row,
                a3_gradiant_max_col = a3_gradiant_max_col,

                a4_max = a4_max,
                a4_min = a4_min,
                a4_max_row = a4_max_row,
                a4_max_col = a4_max_col,
                a4_min_row = a4_min_row,
                a4_min_col = a4_min_col,
                a4_gradiant_max = a4_gradiant_max,
                a4_gradiant_max_row = a4_gradiant_max_row,
                a4_gradiant_max_col = a4_gradiant_max_col,

                // Region B extreme values and gradients
                b1_max = b1_max,
                b1_min = b1_min,
                b1_max_row = b1_max_row,
                b1_max_col = b1_max_col,
                b1_min_row = b1_min_row,
                b1_min_col = b1_min_col,
                b1_gradiant_max = b1_gradiant_max,
                b1_gradiant_max_row = b1_gradiant_max_row,
                b1_gradiant_max_col = b1_gradiant_max_col,

                b2_max = b2_max,
                b2_min = b2_min,
                b2_max_row = b2_max_row,
                b2_max_col = b2_max_col,
                b2_min_row = b2_min_row,
                b2_min_col = b2_min_col,
                b2_gradiant_max = b2_gradiant_max,
                b2_gradiant_max_row = b2_gradiant_max_row,
                b2_gradiant_max_col = b2_gradiant_max_col,

                b3_max = b3_max,
                b3_min = b3_min,
                b3_max_row = b3_max_row,
                b3_max_col = b3_max_col,
                b3_min_row = b3_min_row,
                b3_min_col = b3_min_col,
                b3_gradiant_max = b3_gradiant_max,
                b3_gradiant_max_row = b3_gradiant_max_row,
                b3_gradiant_max_col = b3_gradiant_max_col,

                b4_max = b4_max,
                b4_min = b4_min,
                b4_max_row = b4_max_row,
                b4_max_col = b4_max_col,
                b4_min_row = b4_min_row,
                b4_min_col = b4_min_col,
                b4_gradiant_max = b4_gradiant_max,
                b4_gradiant_max_row = b4_gradiant_max_row,
                b4_gradiant_max_col = b4_gradiant_max_col,

                // Region C extreme values and gradients
                c1_max = c1_max,
                c1_min = c1_min,
                c1_max_row = c1_max_row,
                c1_max_col = c1_max_col,
                c1_min_row = c1_min_row,
                c1_min_col = c1_min_col,
                c1_gradiant_max = c1_gradiant_max,
                c1_gradiant_max_row = c1_gradiant_max_row,
                c1_gradiant_max_col = c1_gradiant_max_col,

                c2_max = c2_max,
                c2_min = c2_min,
                c2_max_row = c2_max_row,
                c2_max_col = c2_max_col,
                c2_min_row = c2_min_row,
                c2_min_col = c2_min_col,
                c2_gradiant_max = c2_gradiant_max,
                c2_gradiant_max_row = c2_gradiant_max_row,
                c2_gradiant_max_col = c2_gradiant_max_col,

                c3_max = c3_max,
                c3_min = c3_min,
                c3_max_row = c3_max_row,
                c3_max_col = c3_max_col,
                c3_min_row = c3_min_row,
                c3_min_col = c3_min_col,
                c3_gradiant_max = c3_gradiant_max,
                c3_gradiant_max_row = c3_gradiant_max_row,
                c3_gradiant_max_col = c3_gradiant_max_col,

                c4_max = c4_max,
                c4_min = c4_min,
                c4_max_row = c4_max_row,
                c4_max_col = c4_max_col,
                c4_min_row = c4_min_row,
                c4_min_col = c4_min_col,
                c4_gradiant_max = c4_gradiant_max,
                c4_gradiant_max_row = c4_gradiant_max_row,
                c4_gradiant_max_col = c4_gradiant_max_col
            };
        }
    }
}
