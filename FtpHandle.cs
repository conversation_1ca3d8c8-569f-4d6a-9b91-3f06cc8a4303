﻿using System;
using System.IO;
using System.Net;

class FtpHelper
{
    private static string ftpServer;
    private static string ftpUser;
    private static string ftpPassword;

    public static void FtpSet(string server, string user, string password)
    {
        ftpServer = server;
        ftpUser = user;
        ftpPassword = password;
    }

    // 上传文件到 FTP 服务器
    public static bool UploadFile(string localFilePath, string remoteFilePath)
    {
        try
        {
            // 创建 FTP 请求
            FtpWebRequest request = (FtpWebRequest)WebRequest.Create($"ftp://{ftpServer}/{remoteFilePath}");
            request.Method = WebRequestMethods.Ftp.UploadFile;
            request.Credentials = new NetworkCredential(ftpUser, ftpPassword);

            // 读取本地文件
            byte[] fileContents;
            using (FileStream sourceStream = File.OpenRead(localFilePath))
            {
                fileContents = new byte[sourceStream.Length];
                sourceStream.Read(fileContents, 0, fileContents.Length);
            }

            // 设置请求流
            request.ContentLength = fileContents.Length;
            using (Stream requestStream = request.GetRequestStream())
            {
                requestStream.Write(fileContents, 0, fileContents.Length);
            }

            // 获取响应
            using (FtpWebResponse response = (FtpWebResponse)request.GetResponse())
            {
                Console.WriteLine($"上传状态: {response.StatusDescription}");
            }

            return true;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"上传文件时出错: {ex.Message}");
            return false;
        }
    }

    // 从 FTP 服务器下载文件
    public static bool DownloadFile(string remoteFilePath, string localFilePath)
    {
        try
        {
            // 创建 FTP 请求
            FtpWebRequest request = (FtpWebRequest)WebRequest.Create($"ftp://{ftpServer}/{remoteFilePath}");
            request.Method = WebRequestMethods.Ftp.DownloadFile;
            request.Credentials = new NetworkCredential(ftpUser, ftpPassword);

            // 获取响应
            using (FtpWebResponse response = (FtpWebResponse)request.GetResponse())
            using (Stream responseStream = response.GetResponseStream())
            using (FileStream fileStream = File.Create(localFilePath))
            {
                byte[] buffer = new byte[4096];
                int bytesRead;
                while ((bytesRead = responseStream.Read(buffer, 0, buffer.Length)) != 0)
                {
                    fileStream.Write(buffer, 0, bytesRead);
                }

                Console.WriteLine($"下载状态: {response.StatusDescription}");
            }

            return true;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"下载文件时出错: {ex.Message}");
            return false;
        }
    }
}

class FtpHelper1
{
    private static string ftpServer;
    private static string ftpUser;
    private static string ftpPassword;

    public static void FtpSet(string server, string user, string password)
    {
        ftpServer = server;
        ftpUser = user;
        ftpPassword = password;
    }

    // 上传文件到 FTP 服务器
    public static bool UploadFile(string localFilePath, string remoteFilePath)
    {
        try
        {
            // 创建 FTP 请求
            FtpWebRequest request = (FtpWebRequest)WebRequest.Create($"ftp://{ftpServer}/{remoteFilePath}");
            request.Method = WebRequestMethods.Ftp.UploadFile;
            request.Credentials = new NetworkCredential(ftpUser, ftpPassword);

            // 读取本地文件
            byte[] fileContents;
            using (FileStream sourceStream = File.OpenRead(localFilePath))
            {
                fileContents = new byte[sourceStream.Length];
                sourceStream.Read(fileContents, 0, fileContents.Length);
            }

            // 设置请求流
            request.ContentLength = fileContents.Length;
            using (Stream requestStream = request.GetRequestStream())
            {
                requestStream.Write(fileContents, 0, fileContents.Length);
            }

            // 获取响应
            using (FtpWebResponse response = (FtpWebResponse)request.GetResponse())
            {
                Console.WriteLine($"上传状态: {response.StatusDescription}");
            }

            return true;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"上传文件时出错: {ex.Message}");
            return false;
        }
    }

    // 从 FTP 服务器下载文件
    public static bool DownloadFile(string remoteFilePath, string localFilePath)
    {
        try
        {
            // 创建 FTP 请求
            FtpWebRequest request = (FtpWebRequest)WebRequest.Create($"ftp://{ftpServer}/{remoteFilePath}");
            request.Method = WebRequestMethods.Ftp.DownloadFile;
            request.Credentials = new NetworkCredential(ftpUser, ftpPassword);

            // 获取响应
            using (FtpWebResponse response = (FtpWebResponse)request.GetResponse())
            using (Stream responseStream = response.GetResponseStream())
            using (FileStream fileStream = File.Create(localFilePath))
            {
                byte[] buffer = new byte[4096];
                int bytesRead;
                while ((bytesRead = responseStream.Read(buffer, 0, buffer.Length)) != 0)
                {
                    fileStream.Write(buffer, 0, bytesRead);
                }

                Console.WriteLine($"下载状态: {response.StatusDescription}");
            }

            return true;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"下载文件时出错: {ex.Message}");
            return false;
        }
    }
}